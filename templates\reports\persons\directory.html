{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-address-book text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:persons_reports' %}">تقارير الأشخاص</a></li>
                            <li class="breadcrumb-item active">دليل الأشخاص</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" placeholder="ابحث بالاسم أو الهاتف أو البريد الإلكتروني" value="{{ search }}">
                </div>
                <div class="col-md-5">
                    <label class="form-label">نوع الشخص</label>
                    <select name="person_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in person_types %}
                        <option value="{{ type_code }}" {% if selected_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if persons %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                دليل الأشخاص
                <span class="badge bg-primary">{{ persons.count }} شخص</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الاسم</th>
                            <th>الاسم بالإنجليزية</th>
                            <th>النوع</th>
                            <th>الهاتف</th>
                            <th>الجوال</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for person in persons %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ person.name }}</strong>
                                {% if person.code %}
                                <small class="text-muted d-block">كود: {{ person.code }}</small>
                                {% endif %}
                            </td>
                            <td>{{ person.name_english|default:"-" }}</td>
                            <td>
                                {% if person.person_type == 'CUSTOMER' %}
                                <span class="badge bg-success">{{ person.get_person_type_display }}</span>
                                {% elif person.person_type == 'SUPPLIER' %}
                                <span class="badge bg-primary">{{ person.get_person_type_display }}</span>
                                {% elif person.person_type == 'EMPLOYEE' %}
                                <span class="badge bg-info">{{ person.get_person_type_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ person.get_person_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if person.phone %}
                                <a href="tel:{{ person.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>{{ person.phone }}
                                </a>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if person.mobile %}
                                <a href="tel:{{ person.mobile }}" class="text-decoration-none">
                                    <i class="fas fa-mobile-alt me-1"></i>{{ person.mobile }}
                                </a>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if person.email %}
                                <a href="mailto:{{ person.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>{{ person.email }}
                                </a>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if person.address %}
                                <small>{{ person.address|truncatechars:50 }}</small>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد نتائج</h5>
            <p class="text-muted">لا توجد أشخاص تطابق معايير البحث المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table th {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }
}
</style>
{% endblock %}
