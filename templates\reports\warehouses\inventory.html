{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-clipboard-check text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:warehouses_reports' %}">تقارير المخازن</a></li>
                            <li class="breadcrumb-item active">جرد البضاعة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">المخزن</label>
                    <select name="warehouse_id" class="form-select">
                        <option value="">جميع المخازن</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}" {% if selected_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                            {{ warehouse.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary -->
    {% if items_inventory %}
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ items_inventory|length }}</h4>
                            <p class="mb-0">إجمالي الأصناف</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_value|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي قيمة المخزون</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Data -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل جرد البضاعة
                <span class="badge bg-primary">{{ items_inventory|length }} صنف</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الوحدة</th>
                            <th>الرصيد الحالي</th>
                            <th>الكمية المتاحة</th>
                            <th>الكمية المحجوزة</th>
                            <th>متوسط التكلفة</th>
                            <th>إجمالي القيمة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item_data in items_inventory %}
                        <tr>
                            <td><strong class="text-primary">{{ item_data.item.code }}</strong></td>
                            <td>
                                <strong>{{ item_data.item.name }}</strong>
                                {% if item_data.item.name_english %}
                                <small class="text-muted d-block">{{ item_data.item.name_english }}</small>
                                {% endif %}
                                {% if item_data.warehouse %}
                                <small class="text-info d-block">المخزن: {{ item_data.warehouse.name }}</small>
                                {% endif %}
                            </td>
                            <td>{{ item_data.item.unit.name }}</td>
                            <td>
                                <strong class="{% if item_data.current_balance > 0 %}text-success{% elif item_data.current_balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                    {{ item_data.current_balance|floatformat:2 }}
                                </strong>
                            </td>
                            <td class="text-success">
                                {% if item_data.available_quantity %}
                                {{ item_data.available_quantity|floatformat:2 }}
                                {% else %}
                                {{ item_data.current_balance|floatformat:2 }}
                                {% endif %}
                            </td>
                            <td class="text-warning">
                                {% if item_data.reserved_quantity %}
                                {{ item_data.reserved_quantity|floatformat:2 }}
                                {% else %}
                                0.00
                                {% endif %}
                            </td>
                            <td>
                                {% if item_data.average_cost %}
                                {{ item_data.average_cost|floatformat:2 }}
                                {% else %}
                                {{ item_data.item.cost_price|default:0|floatformat:2 }}
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-primary">{{ item_data.value|floatformat:2 }}</strong>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="7">الإجمالي</th>
                            <th>
                                <strong class="text-primary">{{ total_value|floatformat:2 }}</strong>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أصناف في المخزون</h5>
            <p class="text-muted">لا توجد أصناف لها رصيد في المخازن المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
