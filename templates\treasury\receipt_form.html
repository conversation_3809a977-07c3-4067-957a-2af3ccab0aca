{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-hand-holding-usd text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'treasury:receipt_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الإيصال الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الإيصال الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.receipt_number.id_for_label }}" class="form-label">{{ form.receipt_number.label }}</label>
                                {{ form.receipt_number }}
                                {% if form.receipt_number.errors %}
                                    <div class="text-danger small">{{ form.receipt_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.treasury.id_for_label }}" class="form-label">{{ form.treasury.label }}</label>
                                {{ form.treasury }}
                                {% if form.treasury.errors %}
                                    <div class="text-danger small">{{ form.treasury.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">{{ form.amount.label }}</label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger small">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.payment_method.id_for_label }}" class="form-label">{{ form.payment_method.label }}</label>
                                {{ form.payment_method }}
                                {% if form.payment_method.errors %}
                                    <div class="text-danger small">{{ form.payment_method.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الشيك (تظهر عند اختيار الدفع بشيك) -->
                <div class="card mb-4" id="checkDetails" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-check me-2"></i>
                            تفاصيل الشيك
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.check_number.id_for_label }}" class="form-label">{{ form.check_number.label }}</label>
                                {{ form.check_number }}
                                {% if form.check_number.errors %}
                                    <div class="text-danger small">{{ form.check_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.check_date.id_for_label }}" class="form-label">{{ form.check_date.label }}</label>
                                {{ form.check_date }}
                                {% if form.check_date.errors %}
                                    <div class="text-danger small">{{ form.check_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.check_bank.id_for_label }}" class="form-label">{{ form.check_bank.label }}</label>
                                {{ form.check_bank }}
                                {% if form.check_bank.errors %}
                                    <div class="text-danger small">{{ form.check_bank.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'treasury:receipt_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodField = document.querySelector('select[name="payment_method"]');
    const checkDetailsCard = document.getElementById('checkDetails');
    
    function toggleCheckDetails() {
        if (paymentMethodField.value === 'CHECK') {
            checkDetailsCard.style.display = 'block';
        } else {
            checkDetailsCard.style.display = 'none';
        }
    }
    
    // تحقق من القيمة الحالية عند تحميل الصفحة
    toggleCheckDetails();
    
    // تحقق عند تغيير طريقة الدفع
    paymentMethodField.addEventListener('change', toggleCheckDetails);
    
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحديد تاريخ الشيك افتراضياً
    const checkDateField = document.querySelector('input[type="date"][name="check_date"]');
    if (checkDateField && !checkDateField.value) {
        checkDateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحقق من صحة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(document.querySelector('input[name="amount"]').value);
            const customer = document.querySelector('select[name="customer"]').value;
            const treasury = document.querySelector('select[name="treasury"]').value;
            const description = document.querySelector('input[name="description"]').value.trim();
            
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('يجب إدخال مبلغ صحيح');
                return false;
            }
            
            if (!customer) {
                e.preventDefault();
                alert('يجب اختيار العميل');
                return false;
            }
            
            if (!treasury) {
                e.preventDefault();
                alert('يجب اختيار الخزينة');
                return false;
            }
            
            if (!description) {
                e.preventDefault();
                alert('يجب إدخال بيان الإيصال');
                return false;
            }
            
            // تحقق من تفاصيل الشيك إذا كانت طريقة الدفع شيك
            if (paymentMethodField.value === 'CHECK') {
                const checkNumber = document.querySelector('input[name="check_number"]').value.trim();
                const checkBank = document.querySelector('input[name="check_bank"]').value.trim();
                
                if (!checkNumber) {
                    e.preventDefault();
                    alert('يجب إدخال رقم الشيك');
                    return false;
                }
                
                if (!checkBank) {
                    e.preventDefault();
                    alert('يجب إدخال البنك المسحوب عليه');
                    return false;
                }
            }
        });
    }
});
</script>
{% endblock %}
