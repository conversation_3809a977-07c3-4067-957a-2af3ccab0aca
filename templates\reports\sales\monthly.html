{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:sales_reports' %}">تقارير المبيعات</a></li>
                            <li class="breadcrumb-item active">المبيعات الشهرية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">السنة</label>
                    <select name="year" class="form-select">
                        {% for year_option in years %}
                        <option value="{{ year_option }}" {% if year == year_option|stringformat:"s" %}selected{% endif %}>
                            {{ year_option }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    {% if monthly_sales %}
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_year_sales|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي مبيعات {{ year }}</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_year_invoices }}</h4>
                            <p class="mb-0">إجمالي الفواتير</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ average_year_invoice|floatformat:2 }}</h4>
                            <p class="mb-0">متوسط الفاتورة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Data -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                المبيعات الشهرية لعام {{ year }}
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الشهر</th>
                            <th>المبلغ</th>
                            <th>عدد الفواتير</th>
                            <th>متوسط الفاتورة</th>
                            <th>النسبة من الإجمالي</th>
                            <th>الرسم البياني</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month_data in monthly_sales %}
                        <tr>
                            <td><strong>{{ month_data.month_name }}</strong></td>
                            <td class="text-success">
                                <strong>{{ month_data.amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ month_data.count }}</span>
                            </td>
                            <td>{{ month_data.average|floatformat:2 }}</td>
                            <td>
                                {% if total_year_sales > 0 %}
                                {% widthratio month_data.amount total_year_sales 100 %}%
                                {% else %}
                                0%
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" 
                                         role="progressbar" 
                                         style="width: {% if total_year_sales > 0 %}{% widthratio month_data.amount total_year_sales 100 %}{% else %}0{% endif %}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات مبيعات</h5>
            <p class="text-muted">لا توجد مبيعات في السنة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
