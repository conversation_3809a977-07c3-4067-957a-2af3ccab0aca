{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-trash text-danger me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'branches:branch_list' %}">تعريف الفروع</a></li>
                            <li class="breadcrumb-item active">حذف الفرع</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <p class="mb-3">هل أنت متأكد من رغبتك في حذف الفرع التالي؟</p>

                    <!-- Branch Details -->
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-4"><strong>اسم الفرع:</strong></div>
                                <div class="col-sm-8">{{ branch.name }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>كود الفرع:</strong></div>
                                <div class="col-sm-8">{{ branch.code }}</div>
                            </div>
                            {% if branch.manager_name %}
                            <div class="row">
                                <div class="col-sm-4"><strong>اسم المدير:</strong></div>
                                <div class="col-sm-8">{{ branch.manager_name }}</div>
                            </div>
                            {% endif %}
                            {% if branch.phone %}
                            <div class="row">
                                <div class="col-sm-4"><strong>الهاتف:</strong></div>
                                <div class="col-sm-8">{{ branch.phone }}</div>
                            </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-sm-4"><strong>الحالة:</strong></div>
                                <div class="col-sm-8">
                                    {% if branch.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'branches:branch_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
