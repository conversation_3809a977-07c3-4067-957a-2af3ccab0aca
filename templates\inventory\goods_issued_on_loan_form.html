{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-arrow-up text-warning me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:goods_issued_on_loan_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات السلفة الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات السلفة الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.loan_number.id_for_label }}" class="form-label">{{ form.loan_number.label }}</label>
                                {{ form.loan_number }}
                                {% if form.loan_number.errors %}
                                    <div class="text-danger small">{{ form.loan_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.expected_return_date.id_for_label }}" class="form-label">{{ form.expected_return_date.label }}</label>
                                {{ form.expected_return_date }}
                                {% if form.expected_return_date.errors %}
                                    <div class="text-danger small">{{ form.expected_return_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المُستعير -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            معلومات المُستعير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.borrower_name.id_for_label }}" class="form-label">{{ form.borrower_name.label }}</label>
                                {{ form.borrower_name }}
                                {% if form.borrower_name.errors %}
                                    <div class="text-danger small">{{ form.borrower_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.borrower_phone.id_for_label }}" class="form-label">{{ form.borrower_phone.label }}</label>
                                {{ form.borrower_phone }}
                                {% if form.borrower_phone.errors %}
                                    <div class="text-danger small">{{ form.borrower_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.borrower_address.id_for_label }}" class="form-label">{{ form.borrower_address.label }}</label>
                                {{ form.borrower_address }}
                                {% if form.borrower_address.errors %}
                                    <div class="text-danger small">{{ form.borrower_address.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.loan_reason.id_for_label }}" class="form-label">{{ form.loan_reason.label }}</label>
                                {{ form.loan_reason }}
                                {% if form.loan_reason.errors %}
                                    <div class="text-danger small">{{ form.loan_reason.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أصناف السلفة -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes me-2"></i>
                            أصناف السلفة المنصرفة
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="addItem()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة صنف
                        </button>
                    </div>
                    <div class="card-body">
                        {{ formset.management_form }}
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="items-table">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 25%">الصنف</th>
                                        <th style="width: 15%">الكمية المنصرفة</th>
                                        <th style="width: 15%">القيمة التقديرية للوحدة</th>
                                        <th style="width: 15%">الإجمالي</th>
                                        <th style="width: 10%">حالة البضاعة</th>
                                        <th style="width: 10%">تاريخ الانتهاء</th>
                                        <th style="width: 5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.item }}
                                                {% if form.item.errors %}
                                                    <div class="text-danger small">{{ form.item.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.quantity_issued }}
                                                {% if form.quantity_issued.errors %}
                                                    <div class="text-danger small">{{ form.quantity_issued.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.estimated_unit_value }}
                                                {% if form.estimated_unit_value.errors %}
                                                    <div class="text-danger small">{{ form.estimated_unit_value.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <input type="text" class="form-control total-value" readonly>
                                            </td>
                                            <td>
                                                {{ form.condition_issued }}
                                                {% if form.condition_issued.errors %}
                                                    <div class="text-danger small">{{ form.condition_issued.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.expiry_date }}
                                                {% if form.expiry_date.errors %}
                                                    <div class="text-danger small">{{ form.expiry_date.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if form.DELETE %}
                                                    {{ form.DELETE }}
                                                {% endif %}
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                            {% for hidden in form.hidden_fields %}
                                                {{ hidden }}
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-warning">
                                        <td colspan="3"><strong>الإجمالي العام:</strong></td>
                                        <td><strong id="grand-total">0.00 ر.س</strong></td>
                                        <td colspan="3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تنبيه:</strong> سيتم خصم الكميات المنصرفة من المخزون المحدد تلقائياً عند الحفظ.
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'inventory:goods_issued_on_loan_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let itemIndex = {{ formset.total_form_count }};

function addItem() {
    const tbody = document.querySelector('#items-table tbody');
    const newRow = document.createElement('tr');
    newRow.className = 'item-row';
    newRow.innerHTML = `
        <td>
            <select name="items-${itemIndex}-item" class="form-select" required>
                <option value="">اختر الصنف</option>
                <!-- سيتم ملؤها بـ AJAX -->
            </select>
        </td>
        <td>
            <input type="number" name="items-${itemIndex}-quantity_issued" class="form-control quantity" step="0.001" min="0.001" required>
        </td>
        <td>
            <input type="number" name="items-${itemIndex}-estimated_unit_value" class="form-control unit-value" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control total-value" readonly>
        </td>
        <td>
            <input type="text" name="items-${itemIndex}-condition_issued" class="form-control">
        </td>
        <td>
            <input type="date" name="items-${itemIndex}-expiry_date" class="form-control">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    
    // تحديث عدد النماذج
    itemIndex++;
    document.querySelector('#id_items-TOTAL_FORMS').value = itemIndex;
    
    // إضافة event listeners للحقول الجديدة
    addEventListeners(newRow);
}

function removeItem(button) {
    const row = button.closest('tr');
    const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
    
    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        row.style.display = 'none';
    } else {
        row.remove();
        itemIndex--;
        document.querySelector('#id_items-TOTAL_FORMS').value = itemIndex;
    }
    
    calculateGrandTotal();
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const unitValue = parseFloat(row.querySelector('.unit-value').value) || 0;
    const total = quantity * unitValue;
    
    row.querySelector('.total-value').value = total.toFixed(2);
    calculateGrandTotal();
}

function calculateGrandTotal() {
    let grandTotal = 0;
    document.querySelectorAll('.item-row:not([style*="display: none"]) .total-value').forEach(input => {
        grandTotal += parseFloat(input.value) || 0;
    });
    
    document.getElementById('grand-total').textContent = grandTotal.toFixed(2) + ' ر.س';
}

function addEventListeners(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitValueInput = row.querySelector('.unit-value');
    
    if (quantityInput) {
        quantityInput.addEventListener('input', () => calculateRowTotal(row));
    }
    if (unitValueInput) {
        unitValueInput.addEventListener('input', () => calculateRowTotal(row));
    }
}

// إضافة event listeners للصفوف الموجودة
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.item-row').forEach(addEventListeners);
    calculateGrandTotal();
    
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
