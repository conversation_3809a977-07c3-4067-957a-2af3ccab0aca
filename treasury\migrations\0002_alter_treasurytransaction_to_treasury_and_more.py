# Generated by Django 5.2.2 on 2025-06-06 23:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('purchases', '0002_add_earned_discount'),
        ('sales', '0002_add_sales_models'),
        ('treasury', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='treasurytransaction',
            name='to_treasury',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transaction_transfers_in', to='definitions.treasury', verbose_name='إلى خزينة'),
        ),
        migrations.CreateModel(
            name='CustodyReceiptIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('depositor', models.CharField(max_length=200, verbose_name='المودع')),
                ('depositor_id', models.CharField(blank=True, max_length=50, verbose_name='رقم الهوية')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('purpose', models.CharField(max_length=200, verbose_name='الغرض من الإيداع')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('RETURNED', 'مرجع'), ('CANCELLED', 'ملغي')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع الفعلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إيصال أمانة وارد',
                'verbose_name_plural': 'إيصالات الأمانة الواردة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='CustodyReceiptOut',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('custodian', models.CharField(max_length=200, verbose_name='أمين العهدة')),
                ('custodian_id', models.CharField(blank=True, max_length=50, verbose_name='رقم الهوية')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('purpose', models.CharField(max_length=200, verbose_name='الغرض من الأمانة')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('RETURNED', 'مرجع'), ('CANCELLED', 'ملغي')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع الفعلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إيصال أمانة صادر',
                'verbose_name_plural': 'إيصالات الأمانة الصادرة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='PaymentNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السند')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('PAID', 'مدفوع'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('payment_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.supplier', verbose_name='المورد')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'ورقة دفع',
                'verbose_name_plural': 'أوراق الدفع',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ReceiptNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السند')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('COLLECTED', 'محصل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('collection_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التحصيل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.customer', verbose_name='العميل')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'ورقة قبض',
                'verbose_name_plural': 'أوراق القبض',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='TreasuryTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_out', to='definitions.treasury', verbose_name='من خزينة')),
                ('to_treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_in', to='definitions.treasury', verbose_name='إلى خزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل بين خزائن',
                'verbose_name_plural': 'تحويلات بين الخزائن',
                'ordering': ['-date', '-id'],
            },
        ),
    ]
