{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-sync-alt text-info me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'assets:home' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للأصول
                    </a>
                    <a href="{% url 'assets:asset_renewal_create' %}" class="btn btn-info">
                        <i class="fas fa-plus me-1"></i>
                        إضافة تجديد جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="{{ search_query }}"
                                       placeholder="البحث في تجديدات الأصول (رقم التجديد، اسم الأصل، الوصف)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="{% url 'assets:asset_renewal_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة تجديدات الأصول -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة تجديدات الأصول الثابتة
                        {% if page_obj %}
                            <span class="badge bg-info ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم التجديد</th>
                                        <th>تاريخ التجديد</th>
                                        <th>الأصل</th>
                                        <th>نوع التجديد</th>
                                        <th>التكلفة</th>
                                        <th>المورد/المقاول</th>
                                        <th>يمدد العمر</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for renewal in page_obj %}
                                        <tr>
                                            <td><strong>{{ renewal.renewal_number }}</strong></td>
                                            <td>{{ renewal.renewal_date|date:"d/m/Y" }}</td>
                                            <td>
                                                <div>
                                                    <strong>{{ renewal.asset.name }}</strong>
                                                    <br><small class="text-muted">{{ renewal.asset.asset_code }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                {% if renewal.renewal_type == 'MAINTENANCE' %}
                                                    <span class="badge bg-warning">{{ renewal.get_renewal_type_display }}</span>
                                                {% elif renewal.renewal_type == 'UPGRADE' %}
                                                    <span class="badge bg-success">{{ renewal.get_renewal_type_display }}</span>
                                                {% elif renewal.renewal_type == 'REPAIR' %}
                                                    <span class="badge bg-danger">{{ renewal.get_renewal_type_display }}</span>
                                                {% elif renewal.renewal_type == 'REPLACEMENT' %}
                                                    <span class="badge bg-info">{{ renewal.get_renewal_type_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="text-info fw-bold">
                                                    {{ renewal.cost|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                {% if renewal.supplier %}
                                                    {{ renewal.supplier }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if renewal.extends_useful_life %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        نعم ({{ renewal.additional_years }} سنوات)
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-times me-1"></i>
                                                        لا
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'assets:asset_renewal_detail' renewal.pk %}" 
                                                       class="btn btn-outline-info" 
                                                       title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'assets:asset_renewal_edit' renewal.pk %}" 
                                                       class="btn btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger delete-btn" 
                                                            data-id="{{ renewal.pk }}"
                                                            data-name="{{ renewal.renewal_number }}"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-secondary print-btn" 
                                                            data-id="{{ renewal.pk }}"
                                                            title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-sync-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تجديدات أصول</h5>
                            <p class="text-muted">لم يتم العثور على أي تجديدات أصول مطابقة لمعايير البحث</p>
                            <a href="{% url 'assets:asset_renewal_create' %}" class="btn btn-info">
                                <i class="fas fa-plus me-2"></i>
                                إضافة تجديد جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات حول تجديد الأصول الثابتة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-sync-alt me-2"></i>
                                أنواع التجديد
                            </h6>
                            <ul class="mb-0">
                                <li><strong>صيانة:</strong> صيانة دورية أو وقائية</li>
                                <li><strong>ترقية:</strong> تحسين أداء الأصل</li>
                                <li><strong>إصلاح:</strong> إصلاح عطل أو تلف</li>
                                <li><strong>استبدال جزء:</strong> تغيير قطع معينة</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تأثير على العمر الإنتاجي
                            </h6>
                            <ul class="mb-0">
                                <li>بعض التجديدات تمدد العمر الإنتاجي</li>
                                <li>يؤثر على حساب الإهلاك المستقبلي</li>
                                <li>يجب توثيق التحسينات بدقة</li>
                                <li>احتفظ بجميع فواتير التجديد</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف تجديد أصل
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const renewalId = this.getAttribute('data-id');
            const renewalNumber = this.getAttribute('data-name');
            
            if (confirm(`هل أنت متأكد من حذف تجديد الأصل "${renewalNumber}"؟`)) {
                fetch(`/assets/renewals/${renewalId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        });
    });

    // طباعة تجديد أصل
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const renewalId = this.getAttribute('data-id');
            // يمكن إضافة رابط الطباعة هنا
            alert('سيتم إضافة وظيفة الطباعة قريباً');
        });
    });
});
</script>
{% endblock %}
