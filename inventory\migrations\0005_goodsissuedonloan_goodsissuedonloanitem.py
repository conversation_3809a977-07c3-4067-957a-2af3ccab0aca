# Generated by Django 5.2.2 on 2025-06-06 22:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0004_goodsreceivedonloan_goodsreceivedonloanitem'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsIssuedOnLoan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('loan_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السلفة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('borrower_name', models.CharField(max_length=200, verbose_name='اسم المُستعير')),
                ('borrower_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف المُستعير')),
                ('borrower_address', models.TextField(blank=True, verbose_name='عنوان المُستعير')),
                ('loan_reason', models.CharField(max_length=200, verbose_name='سبب السلفة')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ISSUED', 'منصرفة'), ('PARTIAL_RETURNED', 'مرتجعة جزئياً'), ('RETURNED', 'مرتجعة بالكامل'), ('OVERDUE', 'متأخرة'), ('CANCELLED', 'ملغاة')], default='ISSUED', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_loans_issued', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'بضاعة منصرفة سلفة لدى الغير',
                'verbose_name_plural': 'بضائع منصرفة سلفة لدى الغير',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='GoodsIssuedOnLoanItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_issued', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المنصرفة')),
                ('quantity_returned', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('estimated_unit_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة التقديرية للوحدة')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('condition_issued', models.CharField(blank=True, max_length=100, verbose_name='حالة البضاعة عند الصرف')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.goodsissuedonloan', verbose_name='السلفة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف بضاعة سلفة منصرفة',
                'verbose_name_plural': 'أصناف بضائع السلف المنصرفة',
            },
        ),
    ]
