{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-cogs text-primary me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة مرحلة جديدة
                </a>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في الكود أو الاسم أو الوصف">
                        </div>
                        <div class="col-md-3">
                            <label for="stage_type" class="form-label">نوع المرحلة</label>
                            <select class="form-select" id="stage_type" name="stage_type">
                                <option value="">جميع الأنواع</option>
                                {% for value, label in stage_types %}
                                    <option value="{{ value }}" {% if stage_type_filter == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة مراحل الإنتاج
                        <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التسلسل</th>
                                        <th>الكود</th>
                                        <th>اسم المرحلة</th>
                                        <th>نوع المرحلة</th>
                                        <th>المدة المقدرة</th>
                                        <th>التكلفة/ساعة</th>
                                        <th>المسؤول</th>
                                        <th>حرجة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stage in page_obj %}
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ stage.sequence_number }}</span>
                                            </td>
                                            <td>
                                                <strong>{{ stage.code }}</strong>
                                            </td>
                                            <td>
                                                <a href="{% url 'definitions:production_stage_detail' stage.pk %}" 
                                                   class="text-decoration-none">
                                                    {{ stage.name }}
                                                </a>
                                                {% if stage.description %}
                                                    <br><small class="text-muted">{{ stage.description|truncatechars:50 }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ stage.get_stage_type_display }}</span>
                                            </td>
                                            <td>
                                                {% if stage.estimated_duration_hours %}
                                                    {{ stage.estimated_duration_hours }} ساعة
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if stage.total_cost_per_hour %}
                                                    {{ stage.total_cost_per_hour|floatformat:2 }} ر.س
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if stage.responsible_user %}
                                                    {{ stage.responsible_user.get_full_name|default:stage.responsible_user.username }}
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if stage.is_critical %}
                                                    <span class="badge bg-danger">حرجة</span>
                                                {% else %}
                                                    <span class="badge bg-success">عادية</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'definitions:production_stage_detail' stage.pk %}" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'definitions:production_stage_edit' stage.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteStage({{ stage.pk }}, '{{ stage.name }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if stage_type_filter %}&stage_type={{ stage_type_filter }}{% endif %}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if stage_type_filter %}&stage_type={{ stage_type_filter }}{% endif %}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if stage_type_filter %}&stage_type={{ stage_type_filter }}{% endif %}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if stage_type_filter %}&stage_type={{ stage_type_filter }}{% endif %}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مراحل إنتاج</h5>
                            <p class="text-muted">ابدأ بإضافة مرحلة إنتاج جديدة</p>
                            <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مرحلة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteStage(stageId, stageName) {
    if (confirm(`هل أنت متأكد من حذف مرحلة الإنتاج "${stageName}"؟`)) {
        fetch(`/definitions/production-stages/${stageId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
