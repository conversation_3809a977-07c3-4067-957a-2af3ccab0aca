{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-clipboard-check text-info me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:physical_inventory_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الجرد الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الجرد الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.inventory_number.id_for_label }}" class="form-label">{{ form.inventory_number.label }}</label>
                                {{ form.inventory_number }}
                                {% if form.inventory_number.errors %}
                                    <div class="text-danger small">{{ form.inventory_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.inventory_type.id_for_label }}" class="form-label">{{ form.inventory_type.label }}</label>
                                {{ form.inventory_type }}
                                {% if form.inventory_type.errors %}
                                    <div class="text-danger small">{{ form.inventory_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }}</label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info me-2"></i>
                            معلومات مهمة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        أنواع الجرد
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>جرد شامل:</strong> جرد جميع الأصناف في المخزن</li>
                                        <li><strong>جرد جزئي:</strong> جرد أصناف محددة فقط</li>
                                        <li><strong>جرد دوري:</strong> جرد منتظم حسب الجدولة</li>
                                        <li><strong>جرد عشوائي:</strong> جرد مفاجئ لأصناف عشوائية</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        خطوات الجرد
                                    </h6>
                                    <ol class="mb-0">
                                        <li>إنشاء الجرد وتحديد المعلومات الأساسية</li>
                                        <li>بدء الجرد (سيتم إنشاء قائمة الأصناف تلقائياً)</li>
                                        <li>جرد الأصناف وتسجيل الكميات الفعلية</li>
                                        <li>إكمال الجرد ومراجعة الفروقات</li>
                                        <li>اعتماد الجرد وتطبيق الفروقات على المخزون</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات مهمة -->
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تنبيهات مهمة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger mb-3">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                تأثير الجرد على المخزون
                            </h6>
                            <ul class="mb-0">
                                <li>عند <strong>بدء الجرد</strong>: سيتم إنشاء قائمة بجميع الأصناف الموجودة في المخزن المحدد</li>
                                <li>عند <strong>اعتماد الجرد</strong>: سيتم تطبيق جميع الفروقات على المخزون الفعلي</li>
                                <li>الفروقات الموجبة ستزيد من المخزون والسالبة ستقلل منه</li>
                                <li>لا يمكن التراجع عن الجرد المعتمد</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning mb-0">
                            <h6 class="alert-heading">
                                <i class="fas fa-clock me-2"></i>
                                توقيت الجرد
                            </h6>
                            <ul class="mb-0">
                                <li>يُنصح بإجراء الجرد في أوقات قليلة الحركة</li>
                                <li>تأكد من عدم وجود حركات مخزون أثناء الجرد</li>
                                <li>قم بإيقاف العمليات في المخزن المحدد أثناء الجرد</li>
                                <li>تأكد من دقة العد قبل إكمال الجرد</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'inventory:physical_inventory_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحديد نوع الجرد الافتراضي
    const inventoryTypeField = document.querySelector('select[name="inventory_type"]');
    if (inventoryTypeField && !inventoryTypeField.value) {
        inventoryTypeField.value = 'FULL';
    }
    
    // إضافة تأكيد عند تغيير المخزن
    const warehouseField = document.querySelector('select[name="warehouse"]');
    if (warehouseField) {
        warehouseField.addEventListener('change', function() {
            if (this.value) {
                const selectedOption = this.options[this.selectedIndex];
                const warehouseName = selectedOption.text;
                
                if (confirm(`هل أنت متأكد من اختيار المخزن "${warehouseName}"؟ سيتم جرد جميع الأصناف الموجودة فيه.`)) {
                    // المتابعة
                } else {
                    this.value = '';
                }
            }
        });
    }
    
    // إضافة تحقق من صحة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const warehouse = document.querySelector('select[name="warehouse"]').value;
            const reason = document.querySelector('input[name="reason"]').value.trim();
            
            if (!warehouse) {
                e.preventDefault();
                alert('يجب اختيار المخزن');
                return false;
            }
            
            if (!reason) {
                e.preventDefault();
                alert('يجب تحديد سبب الجرد');
                return false;
            }
            
            // تأكيد نهائي
            const warehouseName = document.querySelector('select[name="warehouse"] option:checked').text;
            if (!confirm(`هل أنت متأكد من إنشاء جرد للمخزن "${warehouseName}"؟`)) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>

<style>
.alert-heading {
    margin-bottom: 0.5rem;
}

.alert ul, .alert ol {
    padding-left: 1.5rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

.card-header.bg-danger {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}
</style>
{% endblock %}
