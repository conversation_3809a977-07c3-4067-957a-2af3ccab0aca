{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-book-open text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item active">اليوميات الإجمالية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-5">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary -->
    {% if transactions %}
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_debit|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المدين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_credit|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي الدائن</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-minus-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ transactions|length }}</h4>
                            <p class="mb-0">إجمالي المعاملات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل اليوميات الإجمالية
                <span class="badge bg-primary">{{ transactions|length }} معاملة</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>المرجع</th>
                            <th>الوصف</th>
                            <th>مدين</th>
                            <th>دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"Y-m-d" }}</td>
                            <td>
                                {% if transaction.type == 'مبيعات' %}
                                <span class="badge bg-success">{{ transaction.type }}</span>
                                {% elif transaction.type == 'مشتريات' %}
                                <span class="badge bg-primary">{{ transaction.type }}</span>
                                {% elif transaction.type == 'خزينة' %}
                                <span class="badge bg-warning">{{ transaction.type }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ transaction.type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ transaction.reference }}</small>
                            </td>
                            <td>{{ transaction.description }}</td>
                            <td class="text-success">
                                {% if transaction.debit > 0 %}
                                {{ transaction.debit|floatformat:2 }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-danger">
                                {% if transaction.credit > 0 %}
                                {{ transaction.credit|floatformat:2 }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4">الإجمالي</th>
                            <th class="text-success">{{ total_debit|floatformat:2 }}</th>
                            <th class="text-danger">{{ total_credit|floatformat:2 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد معاملات</h5>
            <p class="text-muted">لا توجد معاملات في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
