{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-users me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:person_list' %}">الأشخاص والجهات</a></li>
                <li class="breadcrumb-item active">{{ action }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if person %}edit{% else %}plus{% endif %} me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <!-- Basic Information -->
                    <h6 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h6>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-5 mb-3">
                            <label for="{{ form.name_english.id_for_label }}" class="form-label">
                                {{ form.name_english.label }}
                            </label>
                            {{ form.name_english }}
                            {% if form.name_english.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name_english.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.person_type.id_for_label }}" class="form-label">
                                {{ form.person_type.label }}
                            </label>
                            {{ form.person_type }}
                            {% if form.person_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.person_type.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.entity_type.id_for_label }}" class="form-label">
                                {{ form.entity_type.label }}
                            </label>
                            {{ form.entity_type }}
                            {% if form.entity_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.entity_type.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Identity Information -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-id-card me-2"></i>
                        معلومات الهوية
                    </h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.national_id.id_for_label }}" class="form-label">
                                {{ form.national_id.label }}
                            </label>
                            {{ form.national_id }}
                            {% if form.national_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.national_id.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.tax_number.id_for_label }}" class="form-label">
                                {{ form.tax_number.label }}
                            </label>
                            {{ form.tax_number }}
                            {% if form.tax_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.tax_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.commercial_register.id_for_label }}" class="form-label">
                                {{ form.commercial_register.label }}
                            </label>
                            {{ form.commercial_register }}
                            {% if form.commercial_register.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.commercial_register.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-phone me-2"></i>
                        معلومات الاتصال
                    </h6>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.mobile.id_for_label }}" class="form-label">
                                {{ form.mobile.label }}
                            </label>
                            {{ form.mobile }}
                            {% if form.mobile.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.mobile.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.website.id_for_label }}" class="form-label">
                                {{ form.website.label }}
                            </label>
                            {{ form.website }}
                            {% if form.website.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.website.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Address -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        العنوان
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                {{ form.address.label }}
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.address.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.city.id_for_label }}" class="form-label">
                                        {{ form.city.label }}
                                    </label>
                                    {{ form.city }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.city.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.state.id_for_label }}" class="form-label">
                                        {{ form.state.label }}
                                    </label>
                                    {{ form.state }}
                                    {% if form.state.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.state.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.country.id_for_label }}" class="form-label">
                                        {{ form.country.label }}
                                    </label>
                                    {{ form.country }}
                                    {% if form.country.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.country.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.postal_code.id_for_label }}" class="form-label">
                                        {{ form.postal_code.label }}
                                    </label>
                                    {{ form.postal_code }}
                                    {% if form.postal_code.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.postal_code.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Information -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-dollar-sign me-2"></i>
                        المعلومات المالية
                    </h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.credit_limit.id_for_label }}" class="form-label">
                                {{ form.credit_limit.label }}
                            </label>
                            {{ form.credit_limit }}
                            {% if form.credit_limit.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.credit_limit.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.payment_terms.id_for_label }}" class="form-label">
                                {{ form.payment_terms.label }}
                            </label>
                            {{ form.payment_terms }}
                            {% if form.payment_terms.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.payment_terms.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.currency.id_for_label }}" class="form-label">
                                {{ form.currency.label }}
                            </label>
                            {{ form.currency }}
                            {% if form.currency.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.currency.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ action }}
                            </button>
                            <a href="{% url 'definitions:person_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if person %}
                            <button type="button" 
                                    class="btn btn-outline-danger delete-btn" 
                                    data-id="{{ person.pk }}"
                                    data-name="{{ person.name }}">
                                <i class="fas fa-trash me-2"></i>
                                حذف
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase person code
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    if (codeField) {
        codeField.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
});
</script>
{% endblock %}
