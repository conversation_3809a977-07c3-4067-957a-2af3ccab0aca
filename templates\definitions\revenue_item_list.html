{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">
            <i class="fas fa-hand-holding-usd me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'definitions:revenue_item_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة بند إيراد جديد
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="البحث في بنود الإيرادات">
                </div>
            </div>
            <div class="col-md-5">
                <select name="revenue_category" class="form-select">
                    <option value="">جميع الفئات</option>
                    {% for category in revenue_categories %}
                        <option value="{{ category.id }}" {% if revenue_category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <div class="d-flex gap-1">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{% url 'definitions:revenue_item_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Revenue Items Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة بنود الإيرادات
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الكود</th>
                            <th>اسم البند</th>
                            <th>الفئة</th>
                            <th>رقم الحساب</th>
                            <th>دوري</th>
                            <th>ضريبة</th>
                            <th>عمولة</th>
                            <th>متطلبات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for revenue_item in page_obj %}
                        <tr>
                            <td><strong>{{ revenue_item.code }}</strong></td>
                            <td>
                                {{ revenue_item.name }}
                                {% if revenue_item.description %}
                                    <br>
                                    <small class="text-muted">{{ revenue_item.description|truncatechars:50 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-success">{{ revenue_item.revenue_category.name }}</span>
                            </td>
                            <td>
                                {% if revenue_item.account_number %}
                                    <code>{{ revenue_item.account_number }}</code>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if revenue_item.is_recurring %}
                                    <span class="badge bg-info">{{ revenue_item.get_recurring_period_display }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير دوري</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if revenue_item.is_taxable %}
                                    <span class="badge bg-warning">{{ revenue_item.tax_rate|default:"0" }}%</span>
                                {% else %}
                                    <span class="badge bg-secondary">معفى</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if revenue_item.commission_rate %}
                                    <span class="badge bg-primary">{{ revenue_item.commission_rate }}%</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    {% if revenue_item.requires_contract %}
                                        <span class="badge bg-info" title="يتطلب عقد">عقد</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'definitions:revenue_item_edit' revenue_item.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ revenue_item.pk }}"
                                            data-name="{{ revenue_item.name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if revenue_category_filter %}&revenue_category={{ revenue_category_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if revenue_category_filter %}&revenue_category={{ revenue_category_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if revenue_category_filter %}&revenue_category={{ revenue_category_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if revenue_category_filter %}&revenue_category={{ revenue_category_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد بنود إيرادات</h5>
                <p class="text-muted">لم يتم العثور على أي بنود إيرادات مطابقة لمعايير البحث</p>
                <a href="{% url 'definitions:revenue_item_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة بند إيراد جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف بند الإيراد: <strong id="delete-item-name"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    const deleteItemName = document.getElementById('delete-item-name');
    let deleteId = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteId = this.dataset.id;
            deleteItemName.textContent = this.dataset.name;
            deleteModal.show();
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        if (deleteId) {
            fetch(`/definitions/revenue-items/${deleteId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
            });
        }
        deleteModal.hide();
    });
});
</script>
{% endblock %}
