{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-users text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }}</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contact_person.id_for_label }}" class="form-label">{{ form.contact_person.label }}</label>
                                {{ form.contact_person }}
                                {% if form.contact_person.errors %}
                                    <div class="text-danger small">{{ form.contact_person.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-address-book me-1"></i>
                                    معلومات الاتصال
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger small">{{ form.address.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- المعلومات الضريبية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    المعلومات الضريبية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tax_number.id_for_label }}" class="form-label">{{ form.tax_number.label }}</label>
                                {{ form.tax_number }}
                                {% if form.tax_number.errors %}
                                    <div class="text-danger small">{{ form.tax_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- إعدادات الائتمان والخصم -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-credit-card me-1"></i>
                                    إعدادات الائتمان والخصم
                                </h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.credit_limit.id_for_label }}" class="form-label">{{ form.credit_limit.label }}</label>
                                {{ form.credit_limit }}
                                {% if form.credit_limit.errors %}
                                    <div class="text-danger small">{{ form.credit_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.payment_terms.id_for_label }}" class="form-label">{{ form.payment_terms.label }}</label>
                                {{ form.payment_terms }}
                                {% if form.payment_terms.errors %}
                                    <div class="text-danger small">{{ form.payment_terms.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">{{ form.discount_percentage.label }}</label>
                                {{ form.discount_percentage }}
                                {% if form.discount_percentage.errors %}
                                    <div class="text-danger small">{{ form.discount_percentage.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
