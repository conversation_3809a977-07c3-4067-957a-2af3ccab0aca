{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:persons_reports' %}">تقارير الأشخاص</a></li>
                            <li class="breadcrumb-item active">كشف حساب شخص تفصيلي</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">الشخص</label>
                    <select name="person_id" class="form-select" required>
                        <option value="">اختر الشخص</option>
                        {% for person in persons %}
                        <option value="{{ person.id }}" {% if selected_person == person.id|stringformat:"s" %}selected{% endif %}>
                            {{ person.name }} - {{ person.get_person_type_display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if person %}
    <!-- Person Info -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-user me-2"></i>
                معلومات الشخص
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الاسم:</strong> {{ person.name }}</p>
                    <p><strong>النوع:</strong> {{ person.get_person_type_display }}</p>
                </div>
                <div class="col-md-6">
                    {% if person.phone %}
                    <p><strong>الهاتف:</strong> {{ person.phone }}</p>
                    {% endif %}
                    {% if person.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ person.email }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Final Balance -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card {% if final_balance > 0 %}bg-success{% elif final_balance < 0 %}bg-danger{% else %}bg-secondary{% endif %} text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">الرصيد النهائي: {{ final_balance|floatformat:2 }}</h3>
                    <p class="mb-0">
                        {% if final_balance > 0 %}
                        مدين للشركة
                        {% elif final_balance < 0 %}
                        دائن للشركة
                        {% else %}
                        متوازن
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions -->
    {% if transactions %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل المعاملات
                <span class="badge bg-primary">{{ transactions|length }} معاملة</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>المرجع</th>
                            <th>مدين</th>
                            <th>دائن</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"Y-m-d" }}</td>
                            <td>
                                {% if transaction.type == 'مبيعات' %}
                                <span class="badge bg-success">{{ transaction.type }}</span>
                                {% else %}
                                <span class="badge bg-primary">{{ transaction.type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ transaction.reference }}</small>
                            </td>
                            <td class="text-success">
                                {% if transaction.debit > 0 %}
                                {{ transaction.debit|floatformat:2 }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-danger">
                                {% if transaction.credit > 0 %}
                                {{ transaction.credit|floatformat:2 }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <strong class="{% if transaction.balance > 0 %}text-success{% elif transaction.balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                    {{ transaction.balance|floatformat:2 }}
                                </strong>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">اختر شخص لعرض كشف الحساب التفصيلي</h5>
            <p class="text-muted">يرجى اختيار شخص من القائمة أعلاه لعرض تفاصيل جميع المعاملات</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
