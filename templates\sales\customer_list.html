{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-users text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:customer_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    إضافة عميل جديد
                </a>
            </div>

            <!-- Search -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في الكود أو الاسم أو الهاتف أو البريد">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة العملاء
                        <span class="badge bg-success ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم العميل</th>
                                        <th>معلومات الاتصال</th>
                                        <th>حد الائتمان</th>
                                        <th>الخصم الافتراضي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ customer.code }}</strong>
                                            </td>
                                            <td>
                                                <strong>{{ customer.name }}</strong>
                                                {% if customer.contact_person %}
                                                    <br><small class="text-muted">{{ customer.contact_person }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if customer.phone %}
                                                    <i class="fas fa-phone text-primary me-1"></i>{{ customer.phone }}<br>
                                                {% endif %}
                                                {% if customer.email %}
                                                    <i class="fas fa-envelope text-info me-1"></i>{{ customer.email }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if customer.credit_limit > 0 %}
                                                    {{ customer.credit_limit|floatformat:0 }} ر.س
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if customer.discount_percentage > 0 %}
                                                    <span class="badge bg-warning">{{ customer.discount_percentage|floatformat:1 }}%</span>
                                                {% else %}
                                                    <span class="text-muted">لا يوجد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'sales:customer_edit' customer.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteCustomer({{ customer.pk }}, '{{ customer.name }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد عملاء</h5>
                            <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                            <a href="{% url 'sales:customer_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                إضافة عميل جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteCustomer(customerId, customerName) {
    if (confirm(`هل أنت متأكد من حذف العميل "${customerName}"؟`)) {
        fetch(`/sales/customers/${customerId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
