{% extends 'base/base.html' %}
{% load static %}

{% block title %}قائمة الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-users text-info me-2"></i>
                        قائمة الموظفين
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الموظفين</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة موظف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}"
                               placeholder="البحث في الموظفين">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="department" class="form-select">
                        <option value="">جميع الأقسام</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:"s" %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                            {{ status_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الموظفين
                </h5>
                <span class="badge bg-primary">{{ page_obj.paginator.count }} موظف</span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>المرتب</th>
                            <th>تاريخ التعيين</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ employee.employee_number }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ employee.full_name }}</strong>
                                    {% if employee.person.email %}
                                    <small class="text-muted d-block">{{ employee.person.email }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>{{ employee.department.name }}</td>
                            <td>{{ employee.position.name }}</td>
                            <td>
                                <strong class="text-success">{{ employee.current_salary|floatformat:2 }}</strong>
                                <small class="text-muted d-block">{{ employee.salary_system.currency.code }}</small>
                            </td>
                            <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if employee.status == 'ACTIVE' %}
                                <span class="badge bg-success">{{ employee.get_status_display }}</span>
                                {% elif employee.status == 'INACTIVE' %}
                                <span class="badge bg-warning">{{ employee.get_status_display }}</span>
                                {% elif employee.status == 'TERMINATED' %}
                                <span class="badge bg-danger">{{ employee.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ employee.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'hr:employee_detail' employee.pk %}" 
                                       class="btn btn-outline-info" 
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:employee_edit' employee.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ employee.pk }}"
                                            data-name="{{ employee.full_name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد موظفين</h5>
                <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموظف: <strong id="itemName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    const itemNameSpan = document.getElementById('itemName');
    let currentItemId = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            currentItemId = this.dataset.id;
            itemNameSpan.textContent = this.dataset.name;
            deleteModal.show();
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        if (currentItemId) {
            fetch(`/hr/employees/${currentItemId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message);
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
                deleteModal.hide();
            });
        }
    });
});
</script>
{% endblock %}
