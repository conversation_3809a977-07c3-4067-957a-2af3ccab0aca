{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-inbox text-primary me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'treasury:custody_in_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات إيصال الأمانة الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات إيصال الأمانة الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.receipt_number.id_for_label }}" class="form-label">{{ form.receipt_number.label }}</label>
                                {{ form.receipt_number }}
                                {% if form.receipt_number.errors %}
                                    <div class="text-danger small">{{ form.receipt_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.treasury.id_for_label }}" class="form-label">{{ form.treasury.label }}</label>
                                {{ form.treasury }}
                                {% if form.treasury.errors %}
                                    <div class="text-danger small">{{ form.treasury.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">{{ form.amount.label }}</label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger small">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المودع -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            معلومات المودع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.depositor.id_for_label }}" class="form-label">{{ form.depositor.label }}</label>
                                {{ form.depositor }}
                                {% if form.depositor.errors %}
                                    <div class="text-danger small">{{ form.depositor.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.depositor_id.id_for_label }}" class="form-label">{{ form.depositor_id.label }}</label>
                                {{ form.depositor_id }}
                                {% if form.depositor_id.errors %}
                                    <div class="text-danger small">{{ form.depositor_id.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الأمانة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            تفاصيل الأمانة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.purpose.id_for_label }}" class="form-label">{{ form.purpose.label }}</label>
                                {{ form.purpose }}
                                {% if form.purpose.errors %}
                                    <div class="text-danger small">{{ form.purpose.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.expected_return_date.id_for_label }}" class="form-label">{{ form.expected_return_date.label }}</label>
                                {{ form.expected_return_date }}
                                {% if form.expected_return_date.errors %}
                                    <div class="text-danger small">{{ form.expected_return_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات مهمة -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات مهمة حول إيصالات الأمانة الواردة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-primary">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-inbox me-2"></i>
                                        ما هي الأمانة الواردة؟
                                    </h6>
                                    <ul class="mb-0">
                                        <li>مبلغ يُودع من قبل شخص</li>
                                        <li>لغرض محدد ومؤقت</li>
                                        <li>يجب إرجاعه عند الطلب</li>
                                        <li>يُسجل كدخول للخزينة</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-shield-alt me-2"></i>
                                        إدارة الأمانات الواردة
                                    </h6>
                                    <ul class="mb-0">
                                        <li>تحديد المودع بدقة</li>
                                        <li>توضيح الغرض من الإيداع</li>
                                        <li>تحديد تاريخ الإرجاع المتوقع</li>
                                        <li>الاحتفاظ بالمستندات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'treasury:custody_in_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحديد تاريخ الإرجاع المتوقع (30 يوم من اليوم)
    const expectedReturnDateField = document.querySelector('input[type="date"][name="expected_return_date"]');
    if (expectedReturnDateField && !expectedReturnDateField.value) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30);
        expectedReturnDateField.value = futureDate.toISOString().split('T')[0];
    }
    
    // تحقق من صحة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(document.querySelector('input[name="amount"]').value);
            const treasury = document.querySelector('select[name="treasury"]').value;
            const depositor = document.querySelector('input[name="depositor"]').value.trim();
            const purpose = document.querySelector('input[name="purpose"]').value.trim();
            
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('يجب إدخال مبلغ صحيح');
                return false;
            }
            
            if (!treasury) {
                e.preventDefault();
                alert('يجب اختيار الخزينة');
                return false;
            }
            
            if (!depositor) {
                e.preventDefault();
                alert('يجب إدخال اسم المودع');
                return false;
            }
            
            if (!purpose) {
                e.preventDefault();
                alert('يجب تحديد الغرض من الإيداع');
                return false;
            }
        });
    }
});
</script>
{% endblock %}
