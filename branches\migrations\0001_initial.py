# Generated by Django 5.2.2 on 2025-06-07 12:58

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الفرع')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الفرع')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('manager_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم المدير')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BankMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('movement_type', models.CharField(choices=[('DEPOSIT_FROM_BRANCH_TREASURY', 'إيداع بنكي وارد من خزينة الفرع'), ('WITHDRAWAL_TO_BRANCH_TREASURY', 'مسحوب بنكي لخزينة الفرع'), ('DEPOSIT_FROM_BRANCH_BANK', 'إيداع بنكي وارد من بنك الفرع'), ('WITHDRAWAL_TO_BRANCH_BANK', 'مسحوب بنكي لبنك الفرع')], max_length=50, verbose_name='نوع الحركة')),
                ('movement_date', models.DateField(verbose_name='تاريخ الحركة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='المبلغ')),
                ('bank_name', models.CharField(max_length=200, verbose_name='اسم البنك')),
                ('account_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('description', models.TextField(verbose_name='البيان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'حركة بنكية',
                'verbose_name_plural': 'حركات البنوك',
                'ordering': ['-movement_date'],
            },
        ),
        migrations.CreateModel(
            name='BranchOpeningBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_type', models.CharField(choices=[('CASH', 'نقدية'), ('BANK', 'بنك'), ('INVENTORY', 'مخزون'), ('RECEIVABLES', 'مدينون'), ('PAYABLES', 'دائنون'), ('OTHER', 'أخرى')], max_length=50, verbose_name='نوع الحساب')),
                ('account_name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='مدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='دائن')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'قيد افتتاحي للفرع',
                'verbose_name_plural': 'الأقياد الافتتاحية للفروع',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='CashMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('movement_type', models.CharField(choices=[('RECEIVED_FROM_BRANCH', 'نقدية واردة من الفرع'), ('SENT_TO_BRANCH', 'نقدية صادرة للفرع')], max_length=30, verbose_name='نوع الحركة')),
                ('movement_date', models.DateField(verbose_name='تاريخ الحركة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='البيان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'حركة نقدية',
                'verbose_name_plural': 'حركات النقدية',
                'ordering': ['-movement_date'],
            },
        ),
        migrations.CreateModel(
            name='CollectionRevenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revenue_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيراد')),
                ('revenue_date', models.DateField(verbose_name='تاريخ الإيراد')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='المبلغ')),
                ('revenue_type', models.CharField(choices=[('SALES', 'مبيعات'), ('SERVICES', 'خدمات'), ('COMMISSIONS', 'عمولات'), ('OTHER', 'أخرى')], max_length=50, verbose_name='نوع الإيراد')),
                ('customer_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم العميل')),
                ('description', models.TextField(verbose_name='البيان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'إيراد تحصيلي',
                'verbose_name_plural': 'الإيرادات التحصيلية',
                'ordering': ['-revenue_date'],
            },
        ),
        migrations.CreateModel(
            name='GoodsTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='إجمالي المبلغ')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('APPROVED', 'معتمد'), ('TRANSFERRED', 'محول'), ('RECEIVED', 'مستلم'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل بضاعة للفرع',
                'verbose_name_plural': 'تحويلات البضائع للفروع',
                'ordering': ['-transfer_date'],
            },
        ),
    ]
