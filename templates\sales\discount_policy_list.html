{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-percentage text-primary me-2"></i>
                    {{ title }}
                </h2>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة سياسة خصم جديدة
                </a>
            </div>

            <!-- Search -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في اسم السياسة أو الوصف">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <a href="{% url 'sales:discount_policy_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة سياسات الخصم
                        <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم السياسة</th>
                                        <th>نوع الخصم</th>
                                        <th>قيمة الخصم</th>
                                        <th>الشروط</th>
                                        <th>فترة الصلاحية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for policy in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ policy.name }}</strong>
                                                {% if policy.description %}
                                                    <br><small class="text-muted">{{ policy.description|truncatechars:50 }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ policy.get_discount_type_display }}</span>
                                            </td>
                                            <td>
                                                {% if policy.discount_type == 'PERCENTAGE' %}
                                                    <strong class="text-success">{{ policy.discount_percentage|floatformat:1 }}%</strong>
                                                {% elif policy.discount_type == 'FIXED_AMOUNT' %}
                                                    <strong class="text-success">{{ policy.discount_amount|floatformat:2 }} ر.س</strong>
                                                {% else %}
                                                    <span class="text-muted">متغير</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if policy.min_quantity %}
                                                    <div><small>حد أدنى كمية: {{ policy.min_quantity|floatformat:0 }}</small></div>
                                                {% endif %}
                                                {% if policy.min_amount %}
                                                    <div><small>حد أدنى مبلغ: {{ policy.min_amount|floatformat:0 }} ر.س</small></div>
                                                {% endif %}
                                                {% if not policy.min_quantity and not policy.min_amount %}
                                                    <span class="text-muted">بدون شروط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>من:</strong> {{ policy.start_date|date:"d/m/Y" }}
                                                </div>
                                                {% if policy.end_date %}
                                                    <div>
                                                        <strong>إلى:</strong> {{ policy.end_date|date:"d/m/Y" }}
                                                    </div>
                                                {% else %}
                                                    <div class="text-muted">
                                                        <strong>إلى:</strong> غير محدد
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if policy.is_active %}
                                                    <span class="badge bg-success">نشطة</span>
                                                {% else %}
                                                    <span class="badge bg-danger">غير نشطة</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deletePolicy({{ policy.pk }}, '{{ policy.name }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-percentage fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد سياسات خصم</h5>
                            <p class="text-muted">ابدأ بإضافة سياسة خصم جديدة</p>
                            <a href="#" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة سياسة خصم جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deletePolicy(policyId, policyName) {
    if (confirm(`هل أنت متأكد من حذف سياسة الخصم "${policyName}"؟`)) {
        fetch(`/sales/discount-policies/${policyId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
