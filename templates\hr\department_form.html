{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-sitemap text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:department_list' %}">الأقسام</a></li>
                            <li class="breadcrumb-item active">{{ action }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:department_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    كود القسم <span class="text-danger">*</span>
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    اسم القسم <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name_english.id_for_label }}" class="form-label">
                                    الاسم بالإنجليزية
                                </label>
                                {{ form.name_english }}
                                {% if form.name_english.errors %}
                                <div class="text-danger small">{{ form.name_english.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    الوصف
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الهيكل التنظيمي -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-sitemap me-2"></i>
                                    الهيكل التنظيمي
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.parent_department.id_for_label }}" class="form-label">
                                    القسم الرئيسي
                                </label>
                                {{ form.parent_department }}
                                {% if form.parent_department.errors %}
                                <div class="text-danger small">{{ form.parent_department.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">اختر القسم الرئيسي إذا كان هذا قسم فرعي</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.manager.id_for_label }}" class="form-label">
                                    مدير القسم
                                </label>
                                {{ form.manager }}
                                {% if form.manager.errors %}
                                <div class="text-danger small">{{ form.manager.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">اختر الموظف المسؤول عن إدارة هذا القسم</small>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'hr:department_list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate department code if empty
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    
    if (codeField && codeField.value === '' && nameField) {
        nameField.addEventListener('input', function() {
            if (codeField.value === '') {
                // Generate code from name (first 3 characters + random number)
                const name = this.value.trim();
                if (name.length >= 2) {
                    const code = name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 100);
                    codeField.value = code;
                }
            }
        });
    }
});
</script>
{% endblock %}
