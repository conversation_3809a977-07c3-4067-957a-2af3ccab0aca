# Generated by Django 5.2.2 on 2025-06-06 23:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0010_manufacturingorder_manufacturingordermaterial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PhysicalInventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('inventory_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الجرد')),
                ('date', models.DateField(verbose_name='تاريخ الجرد')),
                ('inventory_type', models.CharField(choices=[('FULL', 'جرد شامل'), ('PARTIAL', 'جرد جزئي'), ('CYCLE', 'جرد دوري'), ('SPOT', 'جرد عشوائي')], default='FULL', max_length=20, verbose_name='نوع الجرد')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('IN_PROGRESS', 'قيد التنفيذ'), ('COMPLETED', 'مكتمل'), ('APPROVED', 'معتمد'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب الجرد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_items_counted', models.IntegerField(default=0, verbose_name='عدد الأصناف المجردة')),
                ('total_discrepancies', models.IntegerField(default=0, verbose_name='عدد الفروقات')),
                ('total_value_difference', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي قيمة الفروقات')),
                ('started_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء الجرد')),
                ('completed_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ إكمال الجرد')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_inventories', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_inventories', to=settings.AUTH_USER_MODEL, verbose_name='أكمل الجرد بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('started_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='started_inventories', to=settings.AUTH_USER_MODEL, verbose_name='بدأ الجرد بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'جرد فعلي',
                'verbose_name_plural': 'جرد فعلي',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='PhysicalInventoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('system_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية في النظام')),
                ('counted_quantity', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='الكمية المجردة')),
                ('difference_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='فرق الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('difference_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='قيمة الفرق')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='الموقع في المخزن')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('counted_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الجرد')),
                ('is_counted', models.BooleanField(default=False, verbose_name='تم الجرد')),
                ('discrepancy_reason', models.CharField(blank=True, max_length=200, verbose_name='سبب الفرق')),
                ('counted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='جرد بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('inventory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.physicalinventory', verbose_name='الجرد')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف جرد فعلي',
                'verbose_name_plural': 'أصناف جرد فعلي',
                'unique_together': {('inventory', 'item')},
            },
        ),
    ]
