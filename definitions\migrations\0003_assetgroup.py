# Generated by Django 5.2.2 on 2025-06-06 12:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0002_warehousezone_warehouselocation_itemlocation'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المجموعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('depreciation_method', models.CharField(choices=[('STRAIGHT_LINE', 'القسط الثابت'), ('DECLINING_BALANCE', 'الرصيد المتناقص'), ('UNITS_OF_PRODUCTION', 'وحدات الإنتاج'), ('SUM_OF_YEARS', 'مجموع سنوات الخدمة')], default='STRAIGHT_LINE', max_length=20, verbose_name='طريقة الاستهلاك')),
                ('default_useful_life', models.IntegerField(blank=True, null=True, verbose_name='العمر الافتراضي (سنوات)')),
                ('default_salvage_value_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة القيمة المتبقية (%)')),
                ('asset_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الأصل')),
                ('depreciation_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الاستهلاك')),
                ('accumulated_depreciation_account', models.CharField(blank=True, max_length=20, verbose_name='حساب مجمع الاستهلاك')),
                ('requires_insurance', models.BooleanField(default=False, verbose_name='يتطلب تأمين')),
                ('requires_maintenance', models.BooleanField(default=True, verbose_name='يتطلب صيانة')),
                ('is_depreciable', models.BooleanField(default=True, verbose_name='قابل للاستهلاك')),
                ('asset_category', models.CharField(choices=[('BUILDING', 'مباني'), ('MACHINERY', 'آلات ومعدات'), ('VEHICLE', 'مركبات'), ('FURNITURE', 'أثاث ومفروشات'), ('COMPUTER', 'أجهزة حاسوب'), ('LAND', 'أراضي'), ('INTANGIBLE', 'أصول معنوية'), ('OTHER', 'أخرى')], default='OTHER', max_length=20, verbose_name='فئة الأصل')),
                ('min_cost_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأدنى للتكلفة')),
                ('max_cost_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأقصى للتكلفة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.assetgroup', verbose_name='المجموعة الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مجموعة أصول',
                'verbose_name_plural': 'مجموعات الأصول',
                'ordering': ['name'],
            },
        ),
    ]
