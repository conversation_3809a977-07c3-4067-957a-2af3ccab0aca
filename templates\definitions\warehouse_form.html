{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-warehouse me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:warehouse_list' %}">المخازن</a></li>
                <li class="breadcrumb-item active">{{ action }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if warehouse %}edit{% else %}plus{% endif %} me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <!-- Warehouse Code -->
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Warehouse Name -->
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Location -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="{{ form.location.id_for_label }}" class="form-label">
                                {{ form.location.label }}
                            </label>
                            {{ form.location }}
                            {% if form.location.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.location.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Manager -->
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.manager.id_for_label }}" class="form-label">
                                {{ form.manager.label }}
                            </label>
                            {{ form.manager }}
                            {% if form.manager.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.manager.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Capacity -->
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.capacity.id_for_label }}" class="form-label">
                                {{ form.capacity.label }}
                            </label>
                            <div class="input-group">
                                {{ form.capacity }}
                                <span class="input-group-text">
                                    <i class="fas fa-cube"></i>
                                </span>
                            </div>
                            {% if form.capacity.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.capacity.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                السعة التخزينية للمخزن (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {{ action }}
                                    </button>
                                    <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                                {% if warehouse %}
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ warehouse.pk }}"
                                            data-name="{{ warehouse.name }}">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        {% if warehouse %}
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ warehouse.created_at|date:"d/m/Y H:i" }}</p>
                        {% if warehouse.created_by %}
                            <p><strong>أنشئ بواسطة:</strong> {{ warehouse.created_by.get_full_name|default:warehouse.created_by.username }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if warehouse.updated_at %}
                            <p><strong>تاريخ التحديث:</strong> {{ warehouse.updated_at|date:"d/m/Y H:i" }}</p>
                        {% endif %}
                        {% if warehouse.updated_by %}
                            <p><strong>حُدث بواسطة:</strong> {{ warehouse.updated_by.get_full_name|default:warehouse.updated_by.username }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if warehouse %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المخزن: <strong>{{ warehouse.name }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase warehouse code
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    if (codeField) {
        codeField.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    {% if warehouse %}
    // Delete functionality
    const deleteBtn = document.querySelector('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete');

    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteModal.show();
        });
    }

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            fetch(`/definitions/warehouses/{{ warehouse.pk }}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{% url "definitions:warehouse_list" %}';
                } else {
                    alert('حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
            });
            deleteModal.hide();
        });
    }
    {% endif %}
});
</script>
{% endblock %}
