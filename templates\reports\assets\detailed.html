{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-building text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:assets_reports' %}">تقارير الأصول الثابتة</a></li>
                            <li class="breadcrumb-item active">التقرير التفصيلي</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">فئة الأصل</label>
                    <select name="category_id" class="form-select">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="ACTIVE" {% if selected_status == 'ACTIVE' %}selected{% endif %}>نشط</option>
                        <option value="INACTIVE" {% if selected_status == 'INACTIVE' %}selected{% endif %}>غير نشط</option>
                        <option value="DISPOSED" {% if selected_status == 'DISPOSED' %}selected{% endif %}>مباع</option>
                        <option value="DAMAGED" {% if selected_status == 'DAMAGED' %}selected{% endif %}>تالف</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="ابحث في الاسم أو الكود...">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if assets %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_assets }}</h4>
                            <p class="mb-0">عدد الأصول</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_original_cost|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي التكلفة الأصلية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_accumulated_depreciation|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي الإهلاك المتراكم</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-area"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_book_value|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي القيمة الدفترية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assets Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل الأصول الثابتة
                <span class="badge bg-primary">{{ total_assets }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>كود الأصل</th>
                            <th>اسم الأصل</th>
                            <th>الفئة</th>
                            <th>تاريخ الشراء</th>
                            <th>التكلفة الأصلية</th>
                            <th>معدل الإهلاك</th>
                            <th>الإهلاك المتراكم</th>
                            <th>القيمة الدفترية</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for asset in assets %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ asset.code }}</strong>
                            </td>
                            <td>
                                <strong>{{ asset.name }}</strong>
                                {% if asset.description %}
                                <small class="text-muted d-block">{{ asset.description|truncatechars:50 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if asset.category %}
                                <span class="badge bg-secondary">{{ asset.category.name }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ asset.purchase_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong class="text-success">{{ asset.original_cost|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ asset.depreciation_rate|floatformat:1 }}%</span>
                            </td>
                            <td>
                                <strong class="text-warning">{{ asset.accumulated_depreciation|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-primary">{{ asset.book_value|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if asset.status == 'ACTIVE' %}
                                <span class="badge bg-success">نشط</span>
                                {% elif asset.status == 'INACTIVE' %}
                                <span class="badge bg-secondary">غير نشط</span>
                                {% elif asset.status == 'DISPOSED' %}
                                <span class="badge bg-danger">مباع</span>
                                {% elif asset.status == 'DAMAGED' %}
                                <span class="badge bg-warning">تالف</span>
                                {% else %}
                                <span class="badge bg-dark">{{ asset.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4">الإجمالي</th>
                            <th class="text-success">{{ total_original_cost|floatformat:2 }}</th>
                            <th></th>
                            <th class="text-warning">{{ total_accumulated_depreciation|floatformat:2 }}</th>
                            <th class="text-primary">{{ total_book_value|floatformat:2 }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أصول ثابتة</h5>
            <p class="text-muted">لا توجد أصول ثابتة تطابق معايير البحث المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
