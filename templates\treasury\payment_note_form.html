{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice-dollar text-warning me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'treasury:payment_note_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات ورقة الدفع الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات ورقة الدفع الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.note_number.id_for_label }}" class="form-label">{{ form.note_number.label }}</label>
                                {{ form.note_number }}
                                {% if form.note_number.errors %}
                                    <div class="text-danger small">{{ form.note_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label">{{ form.due_date.label }}</label>
                                {{ form.due_date }}
                                {% if form.due_date.errors %}
                                    <div class="text-danger small">{{ form.due_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.treasury.id_for_label }}" class="form-label">{{ form.treasury.label }}</label>
                                {{ form.treasury }}
                                {% if form.treasury.errors %}
                                    <div class="text-danger small">{{ form.treasury.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">{{ form.supplier.label }}</label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger small">{{ form.supplier.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">{{ form.amount.label }}</label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger small">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات مهمة -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            معلومات مهمة حول أوراق الدفع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-file-invoice-dollar me-2"></i>
                                        ما هي ورقة الدفع؟
                                    </h6>
                                    <ul class="mb-0">
                                        <li>سند دفع مؤجل للمورد</li>
                                        <li>يحدد مبلغ وتاريخ الاستحقاق</li>
                                        <li>يمكن دفعه في تاريخ الاستحقاق</li>
                                        <li>يساعد في إدارة التدفق النقدي</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-danger">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        إدارة الاستحقاقات
                                    </h6>
                                    <ul class="mb-0">
                                        <li>تحديد تاريخ الاستحقاق بدقة</li>
                                        <li>متابعة الأوراق المعلقة</li>
                                        <li>دفع الأوراق في موعدها</li>
                                        <li>تسجيل تاريخ الدفع الفعلي</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'treasury:payment_note_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحديد تاريخ الاستحقاق (30 يوم من اليوم)
    const dueDateField = document.querySelector('input[type="date"][name="due_date"]');
    if (dueDateField && !dueDateField.value) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30);
        dueDateField.value = futureDate.toISOString().split('T')[0];
    }
    
    // تحديث تاريخ الاستحقاق عند تغيير التاريخ
    dateField.addEventListener('change', function() {
        if (this.value) {
            const selectedDate = new Date(this.value);
            selectedDate.setDate(selectedDate.getDate() + 30);
            dueDateField.value = selectedDate.toISOString().split('T')[0];
        }
    });
    
    // تحقق من صحة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(document.querySelector('input[name="amount"]').value);
            const supplier = document.querySelector('select[name="supplier"]').value;
            const treasury = document.querySelector('select[name="treasury"]').value;
            const description = document.querySelector('input[name="description"]').value.trim();
            const date = document.querySelector('input[name="date"]').value;
            const dueDate = document.querySelector('input[name="due_date"]').value;
            
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('يجب إدخال مبلغ صحيح');
                return false;
            }
            
            if (!supplier) {
                e.preventDefault();
                alert('يجب اختيار المورد');
                return false;
            }
            
            if (!treasury) {
                e.preventDefault();
                alert('يجب اختيار الخزينة');
                return false;
            }
            
            if (!description) {
                e.preventDefault();
                alert('يجب إدخال بيان ورقة الدفع');
                return false;
            }
            
            if (!date) {
                e.preventDefault();
                alert('يجب تحديد تاريخ ورقة الدفع');
                return false;
            }
            
            if (!dueDate) {
                e.preventDefault();
                alert('يجب تحديد تاريخ الاستحقاق');
                return false;
            }
            
            // تحقق من أن تاريخ الاستحقاق بعد تاريخ الورقة
            if (new Date(dueDate) <= new Date(date)) {
                e.preventDefault();
                alert('يجب أن يكون تاريخ الاستحقاق بعد تاريخ ورقة الدفع');
                return false;
            }
        });
    }
});
</script>
{% endblock %}
