from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, F
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

# Import models from other apps
from sales.models import SalesInvoice, Customer
from purchases.models import PurchaseInvoice, Supplier
from inventory.models import Stock, StockMovement
from treasury.models import TreasuryTransaction, Receipt, Payment
from banking.models import BankTransaction
from definitions.models import Item, Warehouse, Bank, Treasury


@login_required
def dashboard_home(request):
    """لوحة التحكم الرئيسية"""

    # Get current date and date ranges
    today = timezone.now().date()
    current_month_start = today.replace(day=1)
    last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
    current_year_start = today.replace(month=1, day=1)

    # Sales Statistics
    sales_today = SalesInvoice.objects.filter(
        date=today,
        status__in=['CONFIRMED', 'DELIVERED', 'PAID']
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

    sales_this_month = SalesInvoice.objects.filter(
        date__gte=current_month_start,
        status__in=['CONFIRMED', 'DELIVERED', 'PAID']
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

    sales_this_year = SalesInvoice.objects.filter(
        date__gte=current_year_start,
        status__in=['CONFIRMED', 'DELIVERED', 'PAID']
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

    # Purchase Statistics
    purchases_today = PurchaseInvoice.objects.filter(
        date=today,
        status__in=['CONFIRMED', 'RECEIVED', 'PAID']
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

    purchases_this_month = PurchaseInvoice.objects.filter(
        date__gte=current_month_start,
        status__in=['CONFIRMED', 'RECEIVED', 'PAID']
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

    # Inventory Statistics
    total_items = Item.objects.filter(is_active=True).count()
    low_stock_items = Stock.objects.filter(
        quantity__lte=models.F('item__min_stock'),
        item__is_active=True
    ).count()

    # Treasury and Bank Balances
    treasury_balance = Treasury.objects.filter(is_active=True).aggregate(
        total=Sum('balance')
    )['total'] or Decimal('0')

    bank_balance = Bank.objects.filter(is_active=True).aggregate(
        total=Sum('balance')
    )['total'] or Decimal('0')

    # Customer and Supplier counts
    total_customers = Customer.objects.filter(is_active=True).count()
    total_suppliers = Supplier.objects.filter(is_active=True).count()

    # Recent transactions
    recent_sales = SalesInvoice.objects.filter(
        is_active=True
    ).order_by('-created_at')[:5]

    recent_purchases = PurchaseInvoice.objects.filter(
        is_active=True
    ).order_by('-created_at')[:5]

    recent_stock_movements = StockMovement.objects.filter(
        is_active=True
    ).order_by('-created_at')[:5]

    # Chart data for sales trend (last 7 days)
    sales_chart_data = []
    sales_chart_labels = []

    for i in range(6, -1, -1):
        date = today - timedelta(days=i)
        daily_sales = SalesInvoice.objects.filter(
            date=date,
            status__in=['CONFIRMED', 'DELIVERED', 'PAID']
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

        sales_chart_data.append(float(daily_sales))
        sales_chart_labels.append(date.strftime('%Y-%m-%d'))

    # Monthly comparison data
    monthly_sales = []
    monthly_purchases = []
    monthly_labels = []

    for i in range(5, -1, -1):
        month_start = (current_month_start - timedelta(days=32*i)).replace(day=1)
        next_month = (month_start + timedelta(days=32)).replace(day=1)

        month_sales = SalesInvoice.objects.filter(
            date__gte=month_start,
            date__lt=next_month,
            status__in=['CONFIRMED', 'DELIVERED', 'PAID']
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

        month_purchases = PurchaseInvoice.objects.filter(
            date__gte=month_start,
            date__lt=next_month,
            status__in=['CONFIRMED', 'RECEIVED', 'PAID']
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')

        monthly_sales.append(float(month_sales))
        monthly_purchases.append(float(month_purchases))
        monthly_labels.append(month_start.strftime('%Y-%m'))

    context = {
        'sales_today': sales_today,
        'sales_this_month': sales_this_month,
        'sales_this_year': sales_this_year,
        'purchases_today': purchases_today,
        'purchases_this_month': purchases_this_month,
        'total_items': total_items,
        'low_stock_items': low_stock_items,
        'treasury_balance': treasury_balance,
        'bank_balance': bank_balance,
        'total_customers': total_customers,
        'total_suppliers': total_suppliers,
        'recent_sales': recent_sales,
        'recent_purchases': recent_purchases,
        'recent_stock_movements': recent_stock_movements,
        'sales_chart_data': sales_chart_data,
        'sales_chart_labels': sales_chart_labels,
        'monthly_sales': monthly_sales,
        'monthly_purchases': monthly_purchases,
        'monthly_labels': monthly_labels,
    }

    return render(request, 'dashboard/home.html', context)
