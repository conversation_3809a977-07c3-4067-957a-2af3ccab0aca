# Generated by Django 5.2.2 on 2025-06-07 00:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0010_add_printer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('asset_code', models.CharField(max_length=50, unique=True, verbose_name='كود الأصل')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الأصل')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('purchase_date', models.DateField(verbose_name='تاريخ الشراء')),
                ('purchase_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الشراء')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='المورد')),
                ('invoice_number', models.CharField(blank=True, max_length=100, verbose_name='رقم الفاتورة')),
                ('depreciation_method', models.CharField(choices=[('STRAIGHT_LINE', 'القسط الثابت'), ('DECLINING_BALANCE', 'الرصيد المتناقص'), ('UNITS_OF_PRODUCTION', 'وحدات الإنتاج'), ('SUM_OF_YEARS', 'مجموع سنوات الاستخدام')], default='STRAIGHT_LINE', max_length=20, verbose_name='طريقة الإهلاك')),
                ('useful_life_years', models.IntegerField(verbose_name='العمر الإنتاجي (سنوات)')),
                ('salvage_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة التخريدية')),
                ('depreciation_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='معدل الإهلاك (%)')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('UNDER_MAINTENANCE', 'تحت الصيانة'), ('DISPOSED', 'مستبعد'), ('SOLD', 'مباع')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('responsible_person', models.CharField(blank=True, max_length=200, verbose_name='الشخص المسؤول')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('manufacturer', models.CharField(blank=True, max_length=200, verbose_name='الشركة المصنعة')),
                ('warranty_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء الضمان')),
                ('accumulated_depreciation', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مجمع الإهلاك')),
                ('book_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة الدفترية')),
                ('asset_group', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.assetgroup', verbose_name='مجموعة الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'أصل ثابت',
                'verbose_name_plural': 'الأصول الثابتة',
                'ordering': ['-purchase_date', 'asset_code'],
            },
        ),
        migrations.CreateModel(
            name='AssetMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('maintenance_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الصيانة')),
                ('maintenance_date', models.DateField(verbose_name='تاريخ الصيانة')),
                ('maintenance_type', models.CharField(choices=[('PREVENTIVE', 'صيانة وقائية'), ('CORRECTIVE', 'صيانة تصحيحية'), ('EMERGENCY', 'صيانة طارئة'), ('SCHEDULED', 'صيانة مجدولة')], max_length=20, verbose_name='نوع الصيانة')),
                ('description', models.TextField(verbose_name='وصف الصيانة')),
                ('cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='التكلفة')),
                ('technician', models.CharField(blank=True, max_length=200, verbose_name='الفني')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='المورد')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('next_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصيانة القادمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='assets.asset', verbose_name='الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صيانة أصل',
                'verbose_name_plural': 'صيانة الأصول',
                'ordering': ['-maintenance_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='AssetPurchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('purchase_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الشراء')),
                ('purchase_date', models.DateField(verbose_name='تاريخ الشراء')),
                ('supplier', models.CharField(max_length=200, verbose_name='المورد')),
                ('invoice_number', models.CharField(blank=True, max_length=100, verbose_name='رقم الفاتورة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'شراء أصل',
                'verbose_name_plural': 'مشتريات الأصول',
                'ordering': ['-purchase_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='AssetPurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.IntegerField(default=1, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='assets.asset', verbose_name='الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='assets.assetpurchase')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'عنصر شراء أصل',
                'verbose_name_plural': 'عناصر شراء الأصول',
            },
        ),
        migrations.CreateModel(
            name='AssetRenewal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('renewal_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التجديد')),
                ('renewal_date', models.DateField(verbose_name='تاريخ التجديد')),
                ('renewal_type', models.CharField(choices=[('MAINTENANCE', 'صيانة'), ('UPGRADE', 'ترقية'), ('REPAIR', 'إصلاح'), ('REPLACEMENT', 'استبدال جزء')], max_length=20, verbose_name='نوع التجديد')),
                ('description', models.TextField(verbose_name='وصف التجديد')),
                ('cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='التكلفة')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='المورد/المقاول')),
                ('invoice_number', models.CharField(blank=True, max_length=100, verbose_name='رقم الفاتورة')),
                ('extends_useful_life', models.BooleanField(default=False, verbose_name='يمدد العمر الإنتاجي')),
                ('additional_years', models.IntegerField(default=0, verbose_name='سنوات إضافية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='assets.asset', verbose_name='الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تجديد أصل',
                'verbose_name_plural': 'تجديدات الأصول',
                'ordering': ['-renewal_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='AssetSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sale_number', models.CharField(max_length=50, unique=True, verbose_name='رقم البيع')),
                ('sale_date', models.DateField(verbose_name='تاريخ البيع')),
                ('buyer', models.CharField(max_length=200, verbose_name='المشتري')),
                ('sale_price', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='سعر البيع')),
                ('book_value_at_sale', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='القيمة الدفترية وقت البيع')),
                ('gain_loss_on_sale', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='ربح/خسارة البيع')),
                ('invoice_number', models.CharField(blank=True, max_length=100, verbose_name='رقم الفاتورة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='assets.asset', verbose_name='الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'بيع أصل',
                'verbose_name_plural': 'مبيعات الأصول',
                'ordering': ['-sale_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='DepreciationEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('entry_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')),
                ('entry_date', models.DateField(verbose_name='تاريخ القيد')),
                ('period_year', models.IntegerField(verbose_name='السنة')),
                ('period_month', models.IntegerField(verbose_name='الشهر')),
                ('depreciation_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='مبلغ الإهلاك')),
                ('accumulated_depreciation_before', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='مجمع الإهلاك قبل')),
                ('accumulated_depreciation_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='مجمع الإهلاك بعد')),
                ('book_value_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='القيمة الدفترية بعد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='assets.asset', verbose_name='الأصل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'قيد إهلاك',
                'verbose_name_plural': 'قيود الإهلاك',
                'ordering': ['-entry_date', '-id'],
                'unique_together': {('asset', 'period_year', 'period_month')},
            },
        ),
    ]
