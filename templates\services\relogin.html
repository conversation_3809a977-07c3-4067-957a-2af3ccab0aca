{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-0">
                <i class="fas fa-sign-in-alt text-info me-2"></i>
                {{ title }}
            </h2>
            <p class="text-muted mb-0">تسجيل الدخول كمستخدم آخر دون إغلاق الجلسة</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mx-auto">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        اختيار المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم:</label>
                            <select class="form-select" id="username" name="username" required>
                                <option value="">اختر المستخدم</option>
                                <option value="admin">المدير العام</option>
                                <option value="accountant">المحاسب</option>
                                <option value="sales">موظف المبيعات</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور:</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> سيتم تسجيل خروجك من الحساب الحالي وتسجيل الدخول بالحساب الجديد
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:home' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
