# Docker Compose for Osaric Accounts System
# ملف Docker Compose لنظام حسابات أوساريك

version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: osaric_db
    environment:
      POSTGRES_DB: osaric_accounts
      POSTGRES_USER: osaric_user
      POSTGRES_PASSWORD: osaric_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: osaric_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  # Django Web Application
  web:
    build: .
    container_name: osaric_web
    environment:
      - DEBUG=False
      - SECRET_KEY=your-secret-key-here
      - DB_NAME=osaric_accounts
      - DB_USER=osaric_user
      - DB_PASSWORD=osaric_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    volumes:
      - ./staticfiles:/app/staticfiles
      - ./mediafiles:/app/mediafiles
      - ./logs:/app/logs
      - ./backups:/app/backups
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Worker
  celery:
    build: .
    container_name: osaric_celery
    command: celery -A osaric_accounts worker -l info
    environment:
      - DEBUG=False
      - SECRET_KEY=your-secret-key-here
      - DB_NAME=osaric_accounts
      - DB_USER=osaric_user
      - DB_PASSWORD=osaric_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery-beat:
    build: .
    container_name: osaric_celery_beat
    command: celery -A osaric_accounts beat -l info
    environment:
      - DEBUG=False
      - SECRET_KEY=your-secret-key-here
      - DB_NAME=osaric_accounts
      - DB_USER=osaric_user
      - DB_PASSWORD=osaric_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: osaric_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./staticfiles:/app/staticfiles
      - ./mediafiles:/app/mediafiles
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  default:
    name: osaric_network
