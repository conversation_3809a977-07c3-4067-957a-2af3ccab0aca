# Generated by Django 5.2.2 on 2025-06-06 22:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0006_auto_20250607_0114'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WarehouseTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transfer_reason', models.CharField(max_length=200, verbose_name='سبب التحويل')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('IN_TRANSIT', 'في الطريق'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('shipped_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')),
                ('received_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستلام')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouse_transfers_out', to='definitions.warehouse', verbose_name='من مخزن')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_transfers', to=settings.AUTH_USER_MODEL, verbose_name='استلم بواسطة')),
                ('shipped_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shipped_transfers', to=settings.AUTH_USER_MODEL, verbose_name='شحن بواسطة')),
                ('to_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouse_transfers_in', to='definitions.warehouse', verbose_name='إلى مخزن')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل بين المخازن',
                'verbose_name_plural': 'تحويلات بين المخازن',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_requested', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('quantity_shipped', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المشحونة')),
                ('quantity_received', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستلمة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.warehousetransfer', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف تحويل مخزن',
                'verbose_name_plural': 'أصناف تحويلات المخازن',
            },
        ),
    ]
