{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-list-alt text-info me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:price_list_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات قائمة الأسعار
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }}</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- إعدادات القائمة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2 mb-3">
                                    <i class="fas fa-cog me-1"></i>
                                    إعدادات القائمة
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.price_type.id_for_label }}" class="form-label">{{ form.price_type.label }}</label>
                                {{ form.price_type }}
                                {% if form.price_type.errors %}
                                    <div class="text-danger small">{{ form.price_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">{{ form.currency.label }}</label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small">{{ form.currency.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_default }}
                                    <label class="form-check-label" for="{{ form.is_default.id_for_label }}">
                                        {{ form.is_default.label }}
                                    </label>
                                </div>
                                {% if form.is_default.errors %}
                                    <div class="text-danger small">{{ form.is_default.errors.0 }}</div>
                                {% endif %}
                                <small class="text-muted">ستصبح هذه القائمة هي الافتراضية وسيتم إلغاء الافتراضية من القوائم الأخرى</small>
                            </div>
                        </div>

                        <!-- فترة الصلاحية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    فترة الصلاحية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                                <small class="text-muted">اتركه فارغاً إذا كانت القائمة بدون تاريخ انتهاء</small>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'sales:price_list_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد تاريخ البداية افتراضياً لليوم الحالي
document.addEventListener('DOMContentLoaded', function() {
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    if (!startDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        startDateField.value = today;
    }
});
</script>
{% endblock %}
