{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-university me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:bank_list' %}">البنوك</a></li>
                <li class="breadcrumb-item active">{{ action }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if bank %}edit{% else %}plus{% endif %} me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.branch.id_for_label }}" class="form-label">
                                {{ form.branch.label }}
                            </label>
                            {{ form.branch }}
                            {% if form.branch.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.branch.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.account_number.id_for_label }}" class="form-label">
                                {{ form.account_number.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.account_number }}
                            {% if form.account_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.account_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.account_name.id_for_label }}" class="form-label">
                                {{ form.account_name.label }}
                            </label>
                            {{ form.account_name }}
                            {% if form.account_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.account_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.currency.id_for_label }}" class="form-label">
                                {{ form.currency.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.currency }}
                            {% if form.currency.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.currency.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.balance.id_for_label }}" class="form-label">
                                {{ form.balance.label }}
                            </label>
                            {{ form.balance }}
                            {% if form.balance.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.balance.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="{{ form.contact_info.id_for_label }}" class="form-label">
                                {{ form.contact_info.label }}
                            </label>
                            {{ form.contact_info }}
                            {% if form.contact_info.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.contact_info.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ action }}
                            </button>
                            <a href="{% url 'definitions:bank_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if bank %}
                            <button type="button" 
                                    class="btn btn-outline-danger delete-btn" 
                                    data-id="{{ bank.pk }}"
                                    data-name="{{ bank.name }}">
                                <i class="fas fa-trash me-2"></i>
                                حذف
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
