{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-money-bill-wave text-danger me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:treasury_reports' %}">تقارير الخزينة</a></li>
                            <li class="breadcrumb-item active">المصروفات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">فئة المصروف</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="OPERATIONAL" {% if selected_category == 'OPERATIONAL' %}selected{% endif %}>مصروفات تشغيلية</option>
                        <option value="ADMINISTRATIVE" {% if selected_category == 'ADMINISTRATIVE' %}selected{% endif %}>مصروفات إدارية</option>
                        <option value="SELLING" {% if selected_category == 'SELLING' %}selected{% endif %}>مصروفات بيعية</option>
                        <option value="FINANCIAL" {% if selected_category == 'FINANCIAL' %}selected{% endif %}>مصروفات مالية</option>
                        <option value="OTHER" {% if selected_category == 'OTHER' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if expenses %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المصروفات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_expenses }}</h4>
                            <p class="mb-0">عدد المصروفات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ average_expense|floatformat:2 }}</h4>
                            <p class="mb-0">متوسط المصروف</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل المصروفات
                <span class="badge bg-danger">{{ total_expenses }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>البيان</th>
                            <th>الفئة</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in expenses %}
                        <tr>
                            <td>{{ expense.transaction_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong>{{ expense.description }}</strong>
                                {% if expense.reference_number %}
                                <small class="text-muted d-block">مرجع: {{ expense.reference_number }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if expense.category == 'OPERATIONAL' %}
                                <span class="badge bg-primary">تشغيلية</span>
                                {% elif expense.category == 'ADMINISTRATIVE' %}
                                <span class="badge bg-info">إدارية</span>
                                {% elif expense.category == 'SELLING' %}
                                <span class="badge bg-success">بيعية</span>
                                {% elif expense.category == 'FINANCIAL' %}
                                <span class="badge bg-warning">مالية</span>
                                {% else %}
                                <span class="badge bg-secondary">أخرى</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-danger">{{ expense.amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if expense.payment_method == 'CASH' %}
                                <span class="badge bg-success">نقدي</span>
                                {% elif expense.payment_method == 'BANK' %}
                                <span class="badge bg-primary">بنكي</span>
                                {% elif expense.payment_method == 'CHECK' %}
                                <span class="badge bg-info">شيك</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ expense.payment_method }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if expense.status == 'PENDING' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif expense.status == 'APPROVED' %}
                                <span class="badge bg-success">معتمد</span>
                                {% elif expense.status == 'PAID' %}
                                <span class="badge bg-primary">مدفوع</span>
                                {% elif expense.status == 'CANCELLED' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ expense.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if expense.notes %}
                                <small class="text-muted">{{ expense.notes|truncatechars:50 }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th class="text-danger">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مصروفات</h5>
            <p class="text-muted">لا توجد مصروفات في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
