{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-book text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إنشاء قيد محاسبي جديد</p>
                </div>
                <div>
                    <a href="{% url 'accounting:journal_entries' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        بيانات القيد المحاسبي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        
                        <!-- Basic Info -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="entry_number" class="form-label">رقم القيد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="entry_number" name="entry_number" 
                                       value="{{ form_data.entry_number|default:'' }}" required>
                            </div>
                            <div class="col-md-4">
                                <label for="entry_date" class="form-label">تاريخ القيد <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="entry_date" name="entry_date" 
                                       value="{{ form_data.entry_date|default:'' }}" required>
                            </div>
                            <div class="col-md-4">
                                <label for="description" class="form-label">وصف القيد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="description" name="description" 
                                       value="{{ form_data.description|default:'' }}" required
                                       placeholder="وصف مختصر للقيد">
                            </div>
                        </div>

                        <!-- Journal Entry Lines -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    تفاصيل القيد المحاسبي
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="journal-lines">
                                    <!-- Line 1 -->
                                    <div class="journal-line border rounded p-3 mb-3">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">الحساب <span class="text-danger">*</span></label>
                                                <select class="form-select" name="account_1" required>
                                                    <option value="">اختر الحساب</option>
                                                    {% for account in accounts %}
                                                    <option value="{{ account.id }}">{{ account.code }} - {{ account.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">الشخص</label>
                                                <select class="form-select" name="person_1">
                                                    <option value="">اختر الشخص (اختياري)</option>
                                                    {% for person in persons %}
                                                    <option value="{{ person.id }}">{{ person.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">مدين</label>
                                                <input type="number" class="form-control debit-amount" name="debit_1" 
                                                       step="0.01" min="0" placeholder="0.00">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">دائن</label>
                                                <input type="number" class="form-control credit-amount" name="credit_1" 
                                                       step="0.01" min="0" placeholder="0.00">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">&nbsp;</label>
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-line" disabled>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <label class="form-label">وصف السطر</label>
                                                <input type="text" class="form-control" name="line_description_1" 
                                                       placeholder="وصف تفصيلي للسطر">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Line 2 -->
                                    <div class="journal-line border rounded p-3 mb-3">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">الحساب <span class="text-danger">*</span></label>
                                                <select class="form-select" name="account_2" required>
                                                    <option value="">اختر الحساب</option>
                                                    {% for account in accounts %}
                                                    <option value="{{ account.id }}">{{ account.code }} - {{ account.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">الشخص</label>
                                                <select class="form-select" name="person_2">
                                                    <option value="">اختر الشخص (اختياري)</option>
                                                    {% for person in persons %}
                                                    <option value="{{ person.id }}">{{ person.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">مدين</label>
                                                <input type="number" class="form-control debit-amount" name="debit_2" 
                                                       step="0.01" min="0" placeholder="0.00">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">دائن</label>
                                                <input type="number" class="form-control credit-amount" name="credit_2" 
                                                       step="0.01" min="0" placeholder="0.00">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">&nbsp;</label>
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-line">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <label class="form-label">وصف السطر</label>
                                                <input type="text" class="form-control" name="line_description_2" 
                                                       placeholder="وصف تفصيلي للسطر">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mb-3">
                                    <button type="button" id="add-line" class="btn btn-outline-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة سطر جديد
                                    </button>
                                </div>

                                <!-- Totals -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6>إجمالي المدين</h6>
                                                <h4 id="total-debit" class="text-success">0.00</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6>إجمالي الدائن</h6>
                                                <h4 id="total-credit" class="text-primary">0.00</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <div id="balance-status" class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        القيد غير متوازن
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="ملاحظات إضافية (اختياري)">{{ form_data.notes|default:'' }}</textarea>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'accounting:journal_entries' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success" id="save-entry" disabled>
                                <i class="fas fa-save me-2"></i>
                                حفظ القيد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let lineCounter = 2;

    // Auto-generate entry number if empty
    const entryNumberInput = document.getElementById('entry_number');
    if (!entryNumberInput.value) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
        entryNumberInput.value = `JE-${year}${month}${day}-${time}`;
    }

    // Set today's date if empty
    const dateInput = document.getElementById('entry_date');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }

    // Calculate totals and check balance
    function calculateTotals() {
        let totalDebit = 0;
        let totalCredit = 0;

        document.querySelectorAll('.debit-amount').forEach(input => {
            totalDebit += parseFloat(input.value) || 0;
        });

        document.querySelectorAll('.credit-amount').forEach(input => {
            totalCredit += parseFloat(input.value) || 0;
        });

        document.getElementById('total-debit').textContent = totalDebit.toFixed(2);
        document.getElementById('total-credit').textContent = totalCredit.toFixed(2);

        const balanceStatus = document.getElementById('balance-status');
        const saveButton = document.getElementById('save-entry');

        if (totalDebit === totalCredit && totalDebit > 0) {
            balanceStatus.className = 'alert alert-success';
            balanceStatus.innerHTML = '<i class="fas fa-check me-2"></i>القيد متوازن';
            saveButton.disabled = false;
        } else {
            balanceStatus.className = 'alert alert-warning';
            balanceStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>القيد غير متوازن';
            saveButton.disabled = true;
        }
    }

    // Add event listeners to existing amount inputs
    document.querySelectorAll('.debit-amount, .credit-amount').forEach(input => {
        input.addEventListener('input', calculateTotals);
    });

    // Add new line
    document.getElementById('add-line').addEventListener('click', function() {
        lineCounter++;
        const newLine = document.createElement('div');
        newLine.className = 'journal-line border rounded p-3 mb-3';
        newLine.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">الحساب <span class="text-danger">*</span></label>
                    <select class="form-select" name="account_${lineCounter}" required>
                        <option value="">اختر الحساب</option>
                        {% for account in accounts %}
                        <option value="{{ account.id }}">{{ account.code }} - {{ account.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الشخص</label>
                    <select class="form-select" name="person_${lineCounter}">
                        <option value="">اختر الشخص (اختياري)</option>
                        {% for person in persons %}
                        <option value="{{ person.id }}">{{ person.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">مدين</label>
                    <input type="number" class="form-control debit-amount" name="debit_${lineCounter}" 
                           step="0.01" min="0" placeholder="0.00">
                </div>
                <div class="col-md-2">
                    <label class="form-label">دائن</label>
                    <input type="number" class="form-control credit-amount" name="credit_${lineCounter}" 
                           step="0.01" min="0" placeholder="0.00">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-danger btn-sm remove-line">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <label class="form-label">وصف السطر</label>
                    <input type="text" class="form-control" name="line_description_${lineCounter}" 
                           placeholder="وصف تفصيلي للسطر">
                </div>
            </div>
        `;

        document.getElementById('journal-lines').appendChild(newLine);

        // Add event listeners to new amount inputs
        newLine.querySelectorAll('.debit-amount, .credit-amount').forEach(input => {
            input.addEventListener('input', calculateTotals);
        });

        // Add remove functionality
        newLine.querySelector('.remove-line').addEventListener('click', function() {
            newLine.remove();
            calculateTotals();
            updateRemoveButtons();
        });

        updateRemoveButtons();
    });

    // Remove line functionality
    document.querySelectorAll('.remove-line').forEach(button => {
        button.addEventListener('click', function() {
            button.closest('.journal-line').remove();
            calculateTotals();
            updateRemoveButtons();
        });
    });

    // Update remove buttons (disable if only 2 lines)
    function updateRemoveButtons() {
        const lines = document.querySelectorAll('.journal-line');
        const removeButtons = document.querySelectorAll('.remove-line');
        
        removeButtons.forEach(button => {
            button.disabled = lines.length <= 2;
        });
    }

    // Initial calculation
    calculateTotals();
    updateRemoveButtons();
});
</script>
{% endblock %}
