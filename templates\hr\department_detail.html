{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل القسم - {{ department.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-sitemap text-primary me-2"></i>
                        تفاصيل القسم
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:department_list' %}">الأقسام</a></li>
                            <li class="breadcrumb-item active">{{ department.name }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:department_edit' department.pk %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <a href="{% url 'hr:department_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات القسم -->
        <div class="col-lg-8">
            <!-- المعلومات الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">كود القسم</label>
                            <div class="fw-bold text-primary fs-5">{{ department.code }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">اسم القسم</label>
                            <div class="fw-bold">{{ department.name }}</div>
                        </div>
                        {% if department.name_english %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الاسم بالإنجليزية</label>
                            <div>{{ department.name_english }}</div>
                        </div>
                        {% endif %}
                        {% if department.parent_department %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">القسم الرئيسي</label>
                            <div>
                                <a href="{% url 'hr:department_detail' department.parent_department.pk %}" class="text-decoration-none">
                                    {{ department.parent_department.name }}
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        {% if department.manager %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">مدير القسم</label>
                            <div>
                                <a href="{% url 'hr:employee_detail' department.manager.pk %}" class="text-decoration-none">
                                    {{ department.manager.full_name }}
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        {% if department.description %}
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">الوصف</label>
                            <div>{{ department.description }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- الأقسام الفرعية -->
            {% if sub_departments %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>
                        الأقسام الفرعية
                        <span class="badge bg-primary">{{ sub_departments.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الكود</th>
                                    <th>الاسم</th>
                                    <th>المدير</th>
                                    <th>عدد الموظفين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sub_dept in sub_departments %}
                                <tr>
                                    <td><strong>{{ sub_dept.code }}</strong></td>
                                    <td>{{ sub_dept.name }}</td>
                                    <td>
                                        {% if sub_dept.manager %}
                                        {{ sub_dept.manager.full_name }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ sub_dept.employees.count }}</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'hr:department_detail' sub_dept.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- المناصب -->
            {% if positions %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        المناصب في هذا القسم
                        <span class="badge bg-success">{{ positions.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المنصب</th>
                                    <th>عدد الموظفين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for position in positions %}
                                <tr>
                                    <td><strong>{{ position.code }}</strong></td>
                                    <td>{{ position.name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ position.employees.count }}</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'hr:position_detail' position.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الموظفين -->
            {% if employees %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        الموظفين في هذا القسم
                        <span class="badge bg-warning">{{ employees.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الموظف</th>
                                    <th>الاسم</th>
                                    <th>المنصب</th>
                                    <th>المرتب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td><strong>{{ employee.employee_number }}</strong></td>
                                    <td>{{ employee.full_name }}</td>
                                    <td>{{ employee.position.name }}</td>
                                    <td>
                                        <strong class="text-success">{{ employee.current_salary|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {% if employee.status == 'ACTIVE' %}
                                        <span class="badge bg-success">{{ employee.get_status_display }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ employee.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>عدد الموظفين:</span>
                            <strong class="text-primary">{{ employees.count }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>عدد المناصب:</span>
                            <strong class="text-success">{{ positions.count }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>الأقسام الفرعية:</span>
                            <strong class="text-info">{{ sub_departments.count }}</strong>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>تاريخ الإنشاء:</span>
                            <small>{{ department.created_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>آخر تحديث:</span>
                            <small>{{ department.updated_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إجراءات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'hr:department_edit' department.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            تعديل القسم
                        </a>
                        <a href="{% url 'hr:position_create' %}" class="btn btn-outline-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة منصب جديد
                        </a>
                        <a href="{% url 'hr:employee_create' %}" class="btn btn-outline-info">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة موظف جديد
                        </a>
                        <button type="button" class="btn btn-outline-danger delete-btn" 
                                data-id="{{ department.pk }}"
                                data-name="{{ department.name }}">
                            <i class="fas fa-trash me-2"></i>
                            حذف القسم
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف القسم: <strong id="itemName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButton = document.querySelector('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    const itemNameSpan = document.getElementById('itemName');
    let currentItemId = null;

    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            currentItemId = this.dataset.id;
            itemNameSpan.textContent = this.dataset.name;
            deleteModal.show();
        });
    }

    confirmDeleteBtn.addEventListener('click', function() {
        if (currentItemId) {
            fetch(`/hr/departments/${currentItemId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{% url "hr:department_list" %}';
                } else {
                    alert(data.message);
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
                deleteModal.hide();
            });
        }
    });
});
</script>
{% endblock %}
