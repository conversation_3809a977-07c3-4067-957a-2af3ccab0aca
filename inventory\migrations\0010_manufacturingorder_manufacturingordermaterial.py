# Generated by Django 5.2.2 on 2025-06-06 22:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0009_auto_20250607_0138'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ManufacturingOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر الإنتاج')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('quantity_to_produce', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوب إنتاجها')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المنتجة')),
                ('production_cost_per_unit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الإنتاج للوحدة')),
                ('total_production_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة الإنتاج')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('IN_PRODUCTION', 'قيد الإنتاج'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('LOW', 'منخفضة'), ('NORMAL', 'عادية'), ('HIGH', 'عالية'), ('URGENT', 'عاجلة')], default='NORMAL', max_length=20, verbose_name='الأولوية')),
                ('expected_completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز المتوقع')),
                ('actual_completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز الفعلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة المواد')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('started_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء الإنتاج')),
                ('completed_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ إكمال الإنتاج')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='أكمل الإنتاج بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product_item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='manufacturing_orders', to='definitions.item', verbose_name='المنتج النهائي')),
                ('started_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='started_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='بدأ الإنتاج بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'أمر إنتاج تام تصنيع',
                'verbose_name_plural': 'أوامر إنتاج تام تصنيع',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ManufacturingOrderMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_required', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('quantity_consumed', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستهلكة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='المادة الخام')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='inventory.manufacturingorder', verbose_name='أمر الإنتاج')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مادة خام لأمر الإنتاج',
                'verbose_name_plural': 'مواد خام لأوامر الإنتاج',
            },
        ),
    ]
