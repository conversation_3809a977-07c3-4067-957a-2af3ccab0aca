{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل نظام صرف المرتب - {{ salary_system.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-money-bill text-success me-2"></i>
                        تفاصيل نظام صرف المرتب
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:salary_system_list' %}">أنظمة صرف المرتبات</a></li>
                            <li class="breadcrumb-item active">{{ salary_system.name }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:salary_system_edit' salary_system.pk %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <a href="{% url 'hr:salary_system_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات النظام -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">كود النظام</label>
                            <div class="fw-bold">{{ salary_system.code }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">اسم النظام</label>
                            <div class="fw-bold">{{ salary_system.name }}</div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">الوصف</label>
                            <div>{{ salary_system.description|default:"لا يوجد وصف" }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">نوع النظام</label>
                            <div>
                                <span class="badge bg-info fs-6">{{ salary_system.get_system_type_display }}</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">العملة</label>
                            <div>
                                <span class="badge bg-secondary fs-6">{{ salary_system.currency.code }}</span>
                                {{ salary_system.currency.name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات المرتب -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات المرتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الراتب الأساسي</label>
                            <div class="fw-bold text-success fs-5">
                                {{ salary_system.basic_salary|floatformat:2 }} {{ salary_system.currency.code }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">العمل الإضافي</label>
                            <div>
                                {% if salary_system.include_overtime %}
                                <span class="badge bg-success">مفعل</span>
                                <small class="text-muted d-block">معدل: {{ salary_system.overtime_rate }}x</small>
                                {% else %}
                                <span class="badge bg-secondary">غير مفعل</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">نسبة التأمينات الاجتماعية</label>
                            <div class="fw-bold">{{ salary_system.social_insurance_rate }}%</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الإعفاء الضريبي</label>
                            <div class="fw-bold">{{ salary_system.tax_exemption|floatformat:2 }} {{ salary_system.currency.code }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الموظفين المرتبطين -->
            {% if employees %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        الموظفين المرتبطين بهذا النظام
                        <span class="badge bg-primary">{{ employees.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الموظف</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>المرتب الحالي</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>
                                        <a href="{% url 'hr:employee_detail' employee.pk %}" class="text-decoration-none">
                                            <strong>{{ employee.employee_number }}</strong>
                                        </a>
                                    </td>
                                    <td>{{ employee.full_name }}</td>
                                    <td>{{ employee.department.name }}</td>
                                    <td>{{ employee.position.name }}</td>
                                    <td>
                                        <strong class="text-success">{{ employee.current_salary|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {% if employee.status == 'ACTIVE' %}
                                        <span class="badge bg-success">{{ employee.get_status_display }}</span>
                                        {% elif employee.status == 'INACTIVE' %}
                                        <span class="badge bg-warning">{{ employee.get_status_display }}</span>
                                        {% elif employee.status == 'TERMINATED' %}
                                        <span class="badge bg-danger">{{ employee.get_status_display }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ employee.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>عدد الموظفين:</span>
                            <strong class="text-primary">{{ employees.count }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>إجمالي المرتبات الأساسية:</span>
                            <strong class="text-success">
                                {% if employees.count %}
                                {{ employees.count|floatformat:0 }} × {{ salary_system.basic_salary|floatformat:2 }}
                                {% else %}
                                0.00
                                {% endif %}
                                {{ salary_system.currency.code }}
                            </strong>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>تاريخ الإنشاء:</span>
                            <small>{{ salary_system.created_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>آخر تحديث:</span>
                            <small>{{ salary_system.updated_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    {% if salary_system.created_by %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>أنشئ بواسطة:</span>
                            <small>{{ salary_system.created_by.get_full_name|default:salary_system.created_by.username }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إجراءات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'hr:salary_system_edit' salary_system.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            تعديل النظام
                        </a>
                        <a href="{% url 'hr:employee_create' %}" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة موظف جديد
                        </a>
                        <button type="button" class="btn btn-outline-danger delete-btn" 
                                data-id="{{ salary_system.pk }}"
                                data-name="{{ salary_system.name }}">
                            <i class="fas fa-trash me-2"></i>
                            حذف النظام
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف نظام صرف المرتب: <strong id="itemName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButton = document.querySelector('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    const itemNameSpan = document.getElementById('itemName');
    let currentItemId = null;

    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            currentItemId = this.dataset.id;
            itemNameSpan.textContent = this.dataset.name;
            deleteModal.show();
        });
    }

    confirmDeleteBtn.addEventListener('click', function() {
        if (currentItemId) {
            fetch(`/hr/salary-systems/${currentItemId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{% url "hr:salary_system_list" %}';
                } else {
                    alert(data.message);
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
                deleteModal.hide();
            });
        }
    });
});
</script>
{% endblock %}
