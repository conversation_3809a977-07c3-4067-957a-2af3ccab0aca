# Generated by Django 5.2.2 on 2025-06-06 19:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StockIncrease',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('increase_number', models.CharField(max_length=50, unique=True, verbose_name='رقم إذن الزيادة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب الزيادة')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('APPLIED', 'مطبق'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_increases', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'إذن إضافة زيادة',
                'verbose_name_plural': 'أذون إضافة الزيادات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockIncreaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المضافة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('increase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockincrease', verbose_name='إذن الزيادة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف إذن زيادة',
                'verbose_name_plural': 'أصناف أذون الزيادات',
            },
        ),
    ]
