{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-building text-success me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'assets:asset_create' %}" class="btn btn-success me-2">
                        <i class="fas fa-plus me-1"></i>
                        إضافة أصل جديد
                    </a>
                    <a href="{% url 'assets:asset_purchase_create' %}" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-1"></i>
                        شراء أصل
                    </a>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي الأصول
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_assets }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-building fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        الأصول النشطة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_assets }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        تحت الصيانة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ under_maintenance }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-tools fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        مستبعدة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ disposed_assets }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- القيم المالية -->
            <div class="row mb-4">
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        إجمالي التكلفة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_cost|floatformat:2 }} ر.س</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-left-secondary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                        مجمع الإهلاك
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_depreciation|floatformat:2 }} ر.س</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        القيمة الدفترية
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_book_value|floatformat:2 }} ر.س</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calculator fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الأصول حسب المجموعة -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-layer-group me-2"></i>
                                الأصول حسب المجموعة
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if assets_by_group %}
                                {% for group in assets_by_group %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ group.asset_group__name|default:"غير محدد" }}</span>
                                        <span class="badge bg-primary">{{ group.count }}</span>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- الصيانة المطلوبة -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                صيانة مطلوبة
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if upcoming_maintenance %}
                                {% for maintenance in upcoming_maintenance %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>{{ maintenance.asset.name }}</strong>
                                            <br><small class="text-muted">{{ maintenance.next_maintenance_date|date:"d/m/Y" }}</small>
                                        </div>
                                        <span class="badge bg-warning">مطلوب</span>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center">لا توجد صيانة مطلوبة</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أحدث المشتريات -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-success">
                                <i class="fas fa-shopping-cart me-2"></i>
                                أحدث المشتريات
                            </h6>
                            <a href="{% url 'assets:asset_purchase_list' %}" class="btn btn-sm btn-outline-success">
                                عرض الكل
                            </a>
                        </div>
                        <div class="card-body">
                            {% if recent_purchases %}
                                {% for purchase in recent_purchases %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>{{ purchase.purchase_number }}</strong>
                                            <br><small class="text-muted">{{ purchase.supplier }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="text-success fw-bold">{{ purchase.total_amount|floatformat:2 }} ر.س</span>
                                            <br><small class="text-muted">{{ purchase.purchase_date|date:"d/m/Y" }}</small>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center">لا توجد مشتريات حديثة</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- أحدث المبيعات -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-hand-holding-usd me-2"></i>
                                أحدث المبيعات
                            </h6>
                            <a href="{% url 'assets:asset_sale_list' %}" class="btn btn-sm btn-outline-info">
                                عرض الكل
                            </a>
                        </div>
                        <div class="card-body">
                            {% if recent_sales %}
                                {% for sale in recent_sales %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>{{ sale.sale_number }}</strong>
                                            <br><small class="text-muted">{{ sale.buyer }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="text-info fw-bold">{{ sale.sale_price|floatformat:2 }} ر.س</span>
                                            <br><small class="text-muted">{{ sale.sale_date|date:"d/m/Y" }}</small>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center">لا توجد مبيعات حديثة</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-link me-2"></i>
                                روابط سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'assets:asset_list' %}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-list me-2"></i>
                                        قائمة الأصول
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'assets:depreciation_list' %}" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-calculator me-2"></i>
                                        حساب الإهلاك
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'assets:asset_maintenance_list' %}" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-tools me-2"></i>
                                        الصيانة
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'assets:asset_reports' %}" class="btn btn-outline-info w-100">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        التقارير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
