{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-building text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item active">تعريف الفروع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:branch_add' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-10">
                    <input type="text" name="search" class="form-control" placeholder="البحث في اسم الفرع، الكود، أو اسم المدير..." value="{{ search }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Branches List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الفروع
                <span class="badge bg-primary">{{ page_obj.paginator.count }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>كود الفرع</th>
                            <th>اسم الفرع</th>
                            <th>اسم المدير</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for branch in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ branch.code }}</strong>
                            </td>
                            <td>
                                <strong>{{ branch.name }}</strong>
                                {% if branch.email %}
                                <small class="text-muted d-block">{{ branch.email }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if branch.manager_name %}
                                {{ branch.manager_name }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if branch.phone %}
                                {{ branch.phone }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if branch.address %}
                                {{ branch.address|truncatechars:50 }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if branch.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'branches:branch_edit' branch.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'branches:branch_delete' branch.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">السابق</a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فروع</h5>
                <p class="text-muted">لم يتم العثور على أي فروع</p>
                <a href="{% url 'branches:branch_add' %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة فرع جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
