# Production Requirements for Osaric Accounts System
# متطلبات الإنتاج لنظام حسابات أوساريك

# Core Django and Database
Django==5.0.1
psycopg2-binary==2.9.9  # PostgreSQL adapter
python-decouple==3.8

# Django REST Framework for API
djangorestframework==3.14.0
django-cors-headers==4.3.1
djangorestframework-simplejwt==5.3.0

# Database and Migrations
django-extensions==3.2.3

# Internationalization and Localization
django-rosetta==0.10.0

# File handling and Media
Pillow==10.2.0
django-storages==1.14.2

# Authentication and Security
django-allauth==0.57.0
cryptography==42.0.2

# Forms and Validation
django-crispy-forms==2.1
crispy-bootstrap5==2024.2

# Utilities
python-dateutil==2.8.2
openpyxl==3.1.2
reportlab==4.0.8
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Charts and Visualization
django-chartjs==2.3.0

# Caching and Performance
redis==5.0.1
django-redis==5.4.0

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0

# API Documentation
drf-spectacular==0.27.0

# Environment and Configuration
python-dotenv==1.0.0

# Production Web Server
gunicorn==21.2.0
whitenoise==6.6.0

# Monitoring and Logging
sentry-sdk==1.40.6

# Database Backup
django-dbbackup==4.0.2

# Security
django-security==0.17.0
django-ratelimit==4.1.0

# Email
django-ses==3.5.0

# File Compression
django-compressor==4.4

# Health Checks
django-health-check==3.17.0

# Admin Enhancements
django-admin-interface==0.26.1

# Testing (for production testing)
factory-boy==3.3.0
coverage==7.3.2

# Deployment
fabric==3.2.2
