{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-clipboard-list text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:purchases_reports' %}">تقارير المشتريات</a></li>
                            <li class="breadcrumb-item active">أوامر التوريد</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">المورد</label>
                    <select name="supplier_id" class="form-select">
                        <option value="">جميع الموردين</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if selected_supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="PENDING" {% if selected_status == 'PENDING' %}selected{% endif %}>معلق</option>
                        <option value="APPROVED" {% if selected_status == 'APPROVED' %}selected{% endif %}>معتمد</option>
                        <option value="ORDERED" {% if selected_status == 'ORDERED' %}selected{% endif %}>مطلوب</option>
                        <option value="RECEIVED" {% if selected_status == 'RECEIVED' %}selected{% endif %}>مستلم</option>
                        <option value="CANCELLED" {% if selected_status == 'CANCELLED' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if orders %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_orders }}</h4>
                            <p class="mb-0">إجمالي أوامر التوريد</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي قيمة الأوامر</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ pending_orders }}</h4>
                            <p class="mb-0">أوامر معلقة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ received_orders }}</h4>
                            <p class="mb-0">أوامر مستلمة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supply Orders -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل أوامر التوريد
                <span class="badge bg-info">{{ total_orders }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الأمر</th>
                            <th>تاريخ الأمر</th>
                            <th>المورد</th>
                            <th>إجمالي المبلغ</th>
                            <th>تاريخ التسليم المتوقع</th>
                            <th>الحالة</th>
                            <th>المعتمد من</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ order.order_number }}</strong>
                            </td>
                            <td>{{ order.order_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong>{{ order.supplier_name }}</strong>
                                {% if order.supplier_phone %}
                                <small class="text-muted d-block">{{ order.supplier_phone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-primary">{{ order.total_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {{ order.expected_delivery_date|date:"Y-m-d" }}
                                {% now "Y-m-d" as today %}
                                {% if order.expected_delivery_date|date:"Y-m-d" < today and order.status != 'RECEIVED' %}
                                <small class="text-danger d-block">متأخر</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.status == 'PENDING' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif order.status == 'APPROVED' %}
                                <span class="badge bg-info">معتمد</span>
                                {% elif order.status == 'ORDERED' %}
                                <span class="badge bg-primary">مطلوب</span>
                                {% elif order.status == 'RECEIVED' %}
                                <span class="badge bg-success">مستلم</span>
                                {% elif order.status == 'CANCELLED' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ order.status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.approved_by %}
                                <strong>{{ order.approved_by }}</strong>
                                {% if order.approved_date %}
                                <small class="text-muted d-block">{{ order.approved_date|date:"Y-m-d" }}</small>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.notes %}
                                <small class="text-muted">{{ order.notes|truncatechars:30 }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th class="text-primary">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="4"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أوامر توريد</h5>
            <p class="text-muted">لا توجد أوامر توريد في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
