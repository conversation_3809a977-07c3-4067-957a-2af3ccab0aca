{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-invoice text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:persons_reports' %}">تقارير الأشخاص</a></li>
                            <li class="breadcrumb-item active">كشف حساب حسب الفواتير</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">الشخص</label>
                    <select name="person_id" class="form-select" required>
                        <option value="">اختر الشخص</option>
                        {% for person in persons %}
                        <option value="{{ person.id }}" {% if selected_person == person.id %}selected{% endif %}>
                            {{ person.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if person %}
    <!-- Person Info -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-user me-2"></i>
                معلومات الشخص
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الاسم:</strong> {{ person.name }}</p>
                    <p><strong>النوع:</strong> {% if person_type == 'customer' %}عميل{% else %}مورد{% endif %}</p>
                </div>
                <div class="col-md-6">
                    {% if person.phone %}
                    <p><strong>الهاتف:</strong> {{ person.phone }}</p>
                    {% endif %}
                    {% if person.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ person.email }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_sales|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_purchases|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المشتريات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card {% if balance > 0 %}bg-info{% elif balance < 0 %}bg-warning{% else %}bg-secondary{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ balance|floatformat:2 }}</h4>
                            <p class="mb-0">الرصيد النهائي</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Invoices -->
    {% if sales_invoices %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-shopping-cart me-2"></i>
                فواتير المبيعات
                <span class="badge bg-success">{{ sales_invoices.count }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الخصم</th>
                            <th>الضريبة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in sales_invoices %}
                        <tr>
                            <td><strong class="text-primary">{{ invoice.invoice_number }}</strong></td>
                            <td>{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                            <td>{{ invoice.subtotal|floatformat:2 }}</td>
                            <td class="text-warning">{{ invoice.discount_amount|floatformat:2 }}</td>
                            <td class="text-info">{{ invoice.tax_amount|floatformat:2 }}</td>
                            <td><strong class="text-success">{{ invoice.total_amount|floatformat:2 }}</strong></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Purchase Invoices -->
    {% if purchase_invoices %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-truck me-2"></i>
                فواتير المشتريات
                <span class="badge bg-primary">{{ purchase_invoices.count }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الخصم</th>
                            <th>الضريبة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in purchase_invoices %}
                        <tr>
                            <td><strong class="text-primary">{{ invoice.invoice_number }}</strong></td>
                            <td>{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                            <td>{{ invoice.subtotal|floatformat:2 }}</td>
                            <td class="text-warning">{{ invoice.discount_amount|floatformat:2 }}</td>
                            <td class="text-info">{{ invoice.tax_amount|floatformat:2 }}</td>
                            <td><strong class="text-primary">{{ invoice.total_amount|floatformat:2 }}</strong></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">اختر شخص لعرض كشف الحساب</h5>
            <p class="text-muted">يرجى اختيار شخص من القائمة أعلاه لعرض تفاصيل كشف الحساب</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
