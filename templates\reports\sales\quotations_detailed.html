{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-contract text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:sales_reports' %}">تقارير المبيعات</a></li>
                            <li class="breadcrumb-item active">عروض الأسعار التفصيلي</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">العميل</label>
                    <select name="customer_id" class="form-select">
                        <option value="">جميع العملاء</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}" {% if selected_customer == customer.id|stringformat:"s" %}selected{% endif %}>
                            {{ customer.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="DRAFT" {% if selected_status == 'DRAFT' %}selected{% endif %}>مسودة</option>
                        <option value="SENT" {% if selected_status == 'SENT' %}selected{% endif %}>مرسل</option>
                        <option value="ACCEPTED" {% if selected_status == 'ACCEPTED' %}selected{% endif %}>مقبول</option>
                        <option value="REJECTED" {% if selected_status == 'REJECTED' %}selected{% endif %}>مرفوض</option>
                        <option value="EXPIRED" {% if selected_status == 'EXPIRED' %}selected{% endif %}>منتهي الصلاحية</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if quotations %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي قيمة العروض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_quotations }}</h4>
                            <p class="mb-0">عدد العروض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-file-contract"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ accepted_count }}</h4>
                            <p class="mb-0">العروض المقبولة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ average_quotation|floatformat:2 }}</h4>
                            <p class="mb-0">متوسط قيمة العرض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quotations -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل عروض الأسعار
                <span class="badge bg-warning">{{ total_quotations }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم العرض</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>صالح حتى</th>
                            <th>المبلغ الفرعي</th>
                            <th>الخصم</th>
                            <th>الضريبة</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for quotation in quotations %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ quotation.quotation_number }}</strong>
                            </td>
                            <td>{{ quotation.quotation_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong>{{ quotation.customer.name }}</strong>
                                {% if quotation.customer.phone %}
                                <small class="text-muted d-block">{{ quotation.customer.phone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if quotation.valid_until %}
                                {{ quotation.valid_until|date:"Y-m-d" }}
                                {% if quotation.valid_until|date:"Y-m-d" < today|date:"Y-m-d" %}
                                <small class="text-danger d-block">منتهي الصلاحية</small>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ quotation.subtotal|floatformat:2 }}</td>
                            <td class="text-warning">
                                {% if quotation.discount_amount %}
                                {{ quotation.discount_amount|floatformat:2 }}
                                {% else %}
                                0.00
                                {% endif %}
                            </td>
                            <td class="text-info">
                                {% if quotation.tax_amount %}
                                {{ quotation.tax_amount|floatformat:2 }}
                                {% else %}
                                0.00
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-warning">{{ quotation.total_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if quotation.status == 'DRAFT' %}
                                <span class="badge bg-secondary">مسودة</span>
                                {% elif quotation.status == 'SENT' %}
                                <span class="badge bg-info">مرسل</span>
                                {% elif quotation.status == 'ACCEPTED' %}
                                <span class="badge bg-success">مقبول</span>
                                {% elif quotation.status == 'REJECTED' %}
                                <span class="badge bg-danger">مرفوض</span>
                                {% elif quotation.status == 'EXPIRED' %}
                                <span class="badge bg-warning">منتهي الصلاحية</span>
                                {% else %}
                                <span class="badge bg-primary">{{ quotation.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="7">الإجمالي</th>
                            <th class="text-warning">{{ total_amount|floatformat:2 }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عروض أسعار</h5>
            <p class="text-muted">لا توجد عروض أسعار في الفترة المحددة</p>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    هذا التقرير يتطلب وجود عروض أسعار في النظام
                </small>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
