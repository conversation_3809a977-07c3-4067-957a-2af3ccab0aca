{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:sales_reports' %}">تقارير المبيعات</a></li>
                            <li class="breadcrumb-item active">الأرباح</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if profit_items %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_sales|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_cost|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي التكلفة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_profit|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي الربح</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                {% if total_sales > 0 %}
                                {{ profit_margin|floatformat:1 }}%
                                {% else %}
                                0.0%
                                {% endif %}
                            </h4>
                            <p class="mb-0">هامش الربح</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profit Items -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل الأرباح حسب الأصناف
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الصنف</th>
                            <th>الكمية المباعة</th>
                            <th>سعر البيع</th>
                            <th>تكلفة الوحدة</th>
                            <th>إجمالي المبيعات</th>
                            <th>إجمالي التكلفة</th>
                            <th>الربح</th>
                            <th>هامش الربح</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in profit_items %}
                        <tr>
                            <td>
                                <strong>{{ item.item_name }}</strong>
                                <small class="text-muted d-block">{{ item.item_code }}</small>
                            </td>
                            <td>{{ item.quantity|floatformat:2 }}</td>
                            <td>{{ item.unit_price|floatformat:2 }}</td>
                            <td>{{ item.cost_price|floatformat:2 }}</td>
                            <td class="text-success">{{ item.total_sales|floatformat:2 }}</td>
                            <td class="text-warning">{{ item.total_cost|floatformat:2 }}</td>
                            <td>
                                <strong class="{% if item.profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ item.profit|floatformat:2 }}
                                </strong>
                            </td>
                            <td>
                                <span class="badge {% if item.profit_margin >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ item.profit_margin|floatformat:1 }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4">الإجمالي</th>
                            <th class="text-success">{{ total_sales|floatformat:2 }}</th>
                            <th class="text-warning">{{ total_cost|floatformat:2 }}</th>
                            <th class="text-primary">{{ total_profit|floatformat:2 }}</th>
                            <th>
                                <span class="badge bg-info">
                                    {% if total_sales > 0 %}
                                    {{ profit_margin|floatformat:1 }}%
                                    {% else %}
                                    0.0%
                                    {% endif %}
                                </span>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات أرباح</h5>
            <p class="text-muted">لا توجد مبيعات في الفترة المحددة لحساب الأرباح</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
