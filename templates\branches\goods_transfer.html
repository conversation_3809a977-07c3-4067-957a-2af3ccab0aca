{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-truck text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item active">بضاعة مرحلة للفروع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:goods_transfer_add' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة تحويل بضاعة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_transfers }}</h4>
                            <p class="mb-0">إجمالي التحويلات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-truck"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي القيمة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ pending_count }}</h4>
                            <p class="mb-0">معلقة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ received_count }}</h4>
                            <p class="mb-0">مستلمة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control" placeholder="البحث في رقم التحويل أو الفرع..." value="{{ search }}">
                </div>
                <div class="col-md-3">
                    <select name="branch_id" class="form-select">
                        <option value="">جميع الفروع</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if branch_filter == branch.id|stringformat:"s" %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="PENDING" {% if status_filter == 'PENDING' %}selected{% endif %}>معلق</option>
                        <option value="APPROVED" {% if status_filter == 'APPROVED' %}selected{% endif %}>معتمد</option>
                        <option value="TRANSFERRED" {% if status_filter == 'TRANSFERRED' %}selected{% endif %}>محول</option>
                        <option value="RECEIVED" {% if status_filter == 'RECEIVED' %}selected{% endif %}>مستلم</option>
                        <option value="CANCELLED" {% if status_filter == 'CANCELLED' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfers List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تحويلات البضائع
                <span class="badge bg-warning">{{ total_transfers }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم التحويل</th>
                            <th>التاريخ</th>
                            <th>الفرع</th>
                            <th>القيمة</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transfer in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ transfer.transfer_number }}</strong>
                            </td>
                            <td>{{ transfer.transfer_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong>{{ transfer.branch.name }}</strong>
                                <small class="text-muted d-block">{{ transfer.branch.code }}</small>
                            </td>
                            <td>
                                <strong class="text-primary">{{ transfer.total_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if transfer.status == 'PENDING' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif transfer.status == 'APPROVED' %}
                                <span class="badge bg-info">معتمد</span>
                                {% elif transfer.status == 'TRANSFERRED' %}
                                <span class="badge bg-primary">محول</span>
                                {% elif transfer.status == 'RECEIVED' %}
                                <span class="badge bg-success">مستلم</span>
                                {% elif transfer.status == 'CANCELLED' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if transfer.notes %}
                                {{ transfer.notes|truncatechars:30 }}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'branches:goods_transfer_edit' transfer.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'branches:goods_transfer_delete' transfer.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th class="text-primary">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}">السابق</a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تحويلات بضائع</h5>
                <p class="text-muted">لم يتم العثور على أي تحويلات بضائع</p>
                <a href="{% url 'branches:goods_transfer_add' %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة تحويل بضاعة جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
