{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-truck text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item active">بضاعة مرحلة للفروع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:goods_transfer_add' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة تحويل بضاعة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Coming Soon -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-truck fa-4x text-warning mb-4"></i>
                    <h3 class="text-muted mb-3">بضاعة مرحلة للفروع</h3>
                    <p class="text-muted mb-4">
                        هذه الصفحة تحت التطوير. ستتيح لك إدارة تحويلات البضائع من المركز الرئيسي إلى الفروع
                        مع تتبع حالة التحويل ومراحل الاستلام.
                    </p>
                    
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                    <h6>معلق</h6>
                                    <p class="text-muted small">في انتظار الموافقة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-check fa-2x text-info mb-2"></i>
                                    <h6>معتمد</h6>
                                    <p class="text-muted small">تم اعتماد التحويل</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-shipping-fast fa-2x text-primary mb-2"></i>
                                    <h6>محول</h6>
                                    <p class="text-muted small">في الطريق للفرع</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <h6>مستلم</h6>
                                    <p class="text-muted small">تم الاستلام بالفرع</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="{% url 'branches:goods_transfer_add' %}" class="btn btn-success btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        إضافة تحويل بضاعة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
