{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-play-circle text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'branches:opening_balance' %}">القيد الافتتاحي</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات القيد الافتتاحي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- الفرع -->
                            <div class="col-md-6 mb-3">
                                <label for="branch" class="form-label">الفرع <span class="text-danger">*</span></label>
                                <select class="form-select" id="branch" name="branch" required>
                                    <option value="">اختر الفرع</option>
                                    {% for branch in branches %}
                                    <option value="{{ branch.id }}">{{ branch.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- نوع الحساب -->
                            <div class="col-md-6 mb-3">
                                <label for="account_type" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <option value="CASH">نقدية</option>
                                    <option value="BANK">بنك</option>
                                    <option value="INVENTORY">مخزون</option>
                                    <option value="RECEIVABLES">مدينون</option>
                                    <option value="PAYABLES">دائنون</option>
                                    <option value="OTHER">أخرى</option>
                                </select>
                            </div>

                            <!-- اسم الحساب -->
                            <div class="col-md-12 mb-3">
                                <label for="account_name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="account_name" name="account_name" required>
                            </div>

                            <!-- المبلغ المدين -->
                            <div class="col-md-6 mb-3">
                                <label for="debit_amount" class="form-label">مدين</label>
                                <input type="number" class="form-control" id="debit_amount" name="debit_amount" 
                                       step="0.01" min="0" value="0">
                            </div>

                            <!-- المبلغ الدائن -->
                            <div class="col-md-6 mb-3">
                                <label for="credit_amount" class="form-label">دائن</label>
                                <input type="number" class="form-control" id="credit_amount" name="credit_amount" 
                                       step="0.01" min="0" value="0">
                            </div>

                            <!-- التاريخ -->
                            <div class="col-md-6 mb-3">
                                <label for="date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date" name="date" required>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'branches:opening_balance' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('date').value = new Date().toISOString().split('T')[0];
    
    // Validate that either debit or credit has a value
    const debitInput = document.getElementById('debit_amount');
    const creditInput = document.getElementById('credit_amount');
    
    function validateAmounts() {
        const debit = parseFloat(debitInput.value) || 0;
        const credit = parseFloat(creditInput.value) || 0;
        
        if (debit > 0 && credit > 0) {
            alert('لا يمكن أن يكون الحساب مدين ودائن في نفس الوقت');
            return false;
        }
        
        if (debit === 0 && credit === 0) {
            alert('يجب إدخال مبلغ في المدين أو الدائن');
            return false;
        }
        
        return true;
    }
    
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!validateAmounts()) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
