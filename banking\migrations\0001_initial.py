# Generated by Django 5.2.2 on 2025-06-06 10:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BankReconciliation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('reconciliation_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسوية')),
                ('statement_date', models.DateField(verbose_name='تاريخ كشف الحساب')),
                ('statement_balance', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='رصيد كشف الحساب')),
                ('book_balance', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد الدفتري')),
                ('difference', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الفرق')),
                ('status', models.CharField(choices=[('IN_PROGRESS', 'قيد التنفيذ'), ('COMPLETED', 'مكتملة')], default='IN_PROGRESS', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.bank', verbose_name='البنك')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تسوية بنك',
                'verbose_name_plural': 'تسويات البنوك',
                'ordering': ['-statement_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='BankTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المعاملة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transaction_type', models.CharField(choices=[('DEPOSIT', 'إيداع'), ('WITHDRAWAL', 'سحب'), ('TRANSFER_IN', 'تحويل وارد'), ('TRANSFER_OUT', 'تحويل صادر'), ('FEE', 'رسوم'), ('INTEREST', 'فوائد')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='الوصف')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('balance_after', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('COMPLETED', 'مكتملة'), ('CANCELLED', 'ملغية')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.bank', verbose_name='البنك')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('to_bank', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transfers_in', to='definitions.bank', verbose_name='إلى بنك')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة بنكية',
                'verbose_name_plural': 'المعاملات البنكية',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='BankReconciliationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_reconciled', models.BooleanField(default=False, verbose_name='مسوى')),
                ('reconciliation_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسوية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('reconciliation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='banking.bankreconciliation', verbose_name='التسوية')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='banking.banktransaction', verbose_name='المعاملة')),
            ],
            options={
                'verbose_name': 'عنصر تسوية بنك',
                'verbose_name_plural': 'عناصر تسوية البنوك',
            },
        ),
        migrations.CreateModel(
            name='CheckBook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('checkbook_number', models.CharField(max_length=50, unique=True, verbose_name='رقم دفتر الشيكات')),
                ('start_number', models.CharField(max_length=20, verbose_name='رقم الشيك الأول')),
                ('end_number', models.CharField(max_length=20, verbose_name='رقم الشيك الأخير')),
                ('total_checks', models.IntegerField(verbose_name='عدد الشيكات')),
                ('used_checks', models.IntegerField(default=0, verbose_name='الشيكات المستخدمة')),
                ('remaining_checks', models.IntegerField(default=0, verbose_name='الشيكات المتبقية')),
                ('issue_date', models.DateField(verbose_name='تاريخ الإصدار')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.bank', verbose_name='البنك')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'دفتر شيكات',
                'verbose_name_plural': 'دفاتر الشيكات',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='Check',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('check_number', models.CharField(max_length=20, verbose_name='رقم الشيك')),
                ('date', models.DateField(verbose_name='تاريخ الشيك')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('payee', models.CharField(max_length=200, verbose_name='المستفيد')),
                ('memo', models.CharField(blank=True, max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('ISSUED', 'صادر'), ('PRESENTED', 'مقدم للبنك'), ('CLEARED', 'مقبوض'), ('BOUNCED', 'مرتد'), ('CANCELLED', 'ملغي')], default='ISSUED', max_length=20, verbose_name='الحالة')),
                ('clearing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ القبض')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('checkbook', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='banking.checkbook', verbose_name='دفتر الشيكات')),
            ],
            options={
                'verbose_name': 'شيك',
                'verbose_name_plural': 'الشيكات',
                'ordering': ['-date', '-check_number'],
                'unique_together': {('checkbook', 'check_number')},
            },
        ),
    ]
