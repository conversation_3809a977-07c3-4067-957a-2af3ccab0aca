# Generated by Django 5.2.2 on 2025-06-06 10:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='رمز العملة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العملة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('is_base_currency', models.BooleanField(default=False, verbose_name='العملة الأساسية')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'عملة',
                'verbose_name_plural': 'العملات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود البنك')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البنك')),
                ('branch', models.CharField(blank=True, max_length=100, verbose_name='الفرع')),
                ('account_number', models.CharField(max_length=50, verbose_name='رقم الحساب')),
                ('account_name', models.CharField(max_length=100, verbose_name='اسم الحساب')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('contact_info', models.TextField(blank=True, verbose_name='معلومات الاتصال')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
            ],
            options={
                'verbose_name': 'بنك',
                'verbose_name_plural': 'البنوك',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ItemCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الفئة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='definitions.itemcategory', verbose_name='الفئة الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'فئة صنف',
                'verbose_name_plural': 'فئات الأصناف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Treasury',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الخزينة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الخزينة')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_treasuries', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'خزينة',
                'verbose_name_plural': 'الخزائن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='كود الوحدة')),
                ('name', models.CharField(max_length=50, verbose_name='اسم الوحدة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز الوحدة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'وحدة قياس',
                'verbose_name_plural': 'وحدات القياس',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الصنف')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الصنف')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='الباركود')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('cost_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('max_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('image', models.ImageField(blank=True, null=True, upload_to='items/', verbose_name='صورة الصنف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.itemcategory', verbose_name='الفئة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المخزن')),
                ('location', models.TextField(blank=True, verbose_name='الموقع')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='السعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='مدير المخزن')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['name'],
            },
        ),
    ]
