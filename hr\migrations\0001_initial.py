# Generated by Django 5.2.2 on 2025-06-07 02:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0010_add_printer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود القسم')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_departments', to='hr.department', verbose_name='القسم الرئيسي')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('employee_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('hire_date', models.DateField(verbose_name='تاريخ التعيين')),
                ('contract_start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ بداية العقد')),
                ('contract_end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية العقد')),
                ('termination_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الخدمة')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('INACTIVE', 'غير نشط'), ('TERMINATED', 'منتهي الخدمة'), ('SUSPENDED', 'موقوف')], default='ACTIVE', max_length=20, verbose_name='حالة الموظف')),
                ('current_salary', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المرتب الحالي')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.department', verbose_name='القسم')),
                ('person', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to='definitions.person', verbose_name='بيانات الشخص')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفين',
                'ordering': ['employee_number'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.employee', verbose_name='مدير القسم'),
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المنصب')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنصب')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='hr.department', verbose_name='القسم')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'منصب',
                'verbose_name_plural': 'المناصب',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='position',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.position', verbose_name='المنصب'),
        ),
        migrations.CreateModel(
            name='SalarySystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود النظام')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النظام')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('system_type', models.CharField(choices=[('MONTHLY', 'شهري'), ('WEEKLY', 'أسبوعي'), ('DAILY', 'يومي'), ('HOURLY', 'بالساعة'), ('PIECE_RATE', 'بالقطعة')], default='MONTHLY', max_length=20, verbose_name='نوع النظام')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الراتب الأساسي')),
                ('include_overtime', models.BooleanField(default=True, verbose_name='يشمل العمل الإضافي')),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=1.5, max_digits=5, verbose_name='معدل العمل الإضافي')),
                ('social_insurance_rate', models.DecimalField(decimal_places=2, default=14.0, max_digits=5, verbose_name='نسبة التأمينات الاجتماعية (%)')),
                ('tax_exemption', models.DecimalField(decimal_places=2, default=9000, max_digits=12, verbose_name='الإعفاء الضريبي')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نظام صرف المرتب',
                'verbose_name_plural': 'أنظمة صرف المرتبات',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='salary_system',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.salarysystem', verbose_name='نظام المرتب'),
        ),
    ]
