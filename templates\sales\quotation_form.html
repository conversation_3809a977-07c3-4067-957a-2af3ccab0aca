{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-quote-right text-warning me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:quotation_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات عرض السعر
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.quotation_number.id_for_label }}" class="form-label">{{ form.quotation_number.label }}</label>
                                {{ form.quotation_number }}
                                {% if form.quotation_number.errors %}
                                    <div class="text-danger small">{{ form.quotation_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.valid_until.id_for_label }}" class="form-label">{{ form.valid_until.label }}</label>
                                {{ form.valid_until }}
                                {% if form.valid_until.errors %}
                                    <div class="text-danger small">{{ form.valid_until.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- العملة والصرف -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    العملة والصرف
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">{{ form.currency.label }}</label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small">{{ form.currency.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exchange_rate.id_for_label }}" class="form-label">{{ form.exchange_rate.label }}</label>
                                {{ form.exchange_rate }}
                                {% if form.exchange_rate.errors %}
                                    <div class="text-danger small">{{ form.exchange_rate.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- المسؤول -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-user-tie me-1"></i>
                                    المسؤول
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.salesperson.id_for_label }}" class="form-label">{{ form.salesperson.label }}</label>
                                {{ form.salesperson }}
                                {% if form.salesperson.errors %}
                                    <div class="text-danger small">{{ form.salesperson.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الشروط والملاحظات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-file-contract me-1"></i>
                                    الشروط والملاحظات
                                </h6>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.terms_and_conditions.id_for_label }}" class="form-label">{{ form.terms_and_conditions.label }}</label>
                                {{ form.terms_and_conditions }}
                                {% if form.terms_and_conditions.errors %}
                                    <div class="text-danger small">{{ form.terms_and_conditions.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'sales:quotation_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
