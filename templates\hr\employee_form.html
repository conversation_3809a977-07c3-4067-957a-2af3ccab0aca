{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-tie text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:employee_list' %}">الموظفين</a></li>
                            <li class="breadcrumb-item active">{{ action }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات الموظف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- معلومات الشخص -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    معلومات الشخص
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.person.id_for_label }}" class="form-label">
                                    الشخص <span class="text-danger">*</span>
                                </label>
                                {{ form.person }}
                                {% if form.person.errors %}
                                <div class="text-danger small">{{ form.person.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">اختر من قائمة الأشخاص المسجلين</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.employee_number.id_for_label }}" class="form-label">
                                    رقم الموظف <span class="text-danger">*</span>
                                </label>
                                {{ form.employee_number }}
                                {% if form.employee_number.errors %}
                                <div class="text-danger small">{{ form.employee_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الوظيفة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-briefcase me-2"></i>
                                    معلومات الوظيفة
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.department.id_for_label }}" class="form-label">
                                    القسم <span class="text-danger">*</span>
                                </label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                <div class="text-danger small">{{ form.department.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.position.id_for_label }}" class="form-label">
                                    المنصب <span class="text-danger">*</span>
                                </label>
                                {{ form.position }}
                                {% if form.position.errors %}
                                <div class="text-danger small">{{ form.position.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.salary_system.id_for_label }}" class="form-label">
                                    نظام المرتب <span class="text-danger">*</span>
                                </label>
                                {{ form.salary_system }}
                                {% if form.salary_system.errors %}
                                <div class="text-danger small">{{ form.salary_system.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.current_salary.id_for_label }}" class="form-label">
                                    المرتب الحالي <span class="text-danger">*</span>
                                </label>
                                {{ form.current_salary }}
                                {% if form.current_salary.errors %}
                                <div class="text-danger small">{{ form.current_salary.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التواريخ -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar me-2"></i>
                                    التواريخ المهمة
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.hire_date.id_for_label }}" class="form-label">
                                    تاريخ التعيين <span class="text-danger">*</span>
                                </label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                <div class="text-danger small">{{ form.hire_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contract_start_date.id_for_label }}" class="form-label">
                                    تاريخ بداية العقد
                                </label>
                                {{ form.contract_start_date }}
                                {% if form.contract_start_date.errors %}
                                <div class="text-danger small">{{ form.contract_start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contract_end_date.id_for_label }}" class="form-label">
                                    تاريخ نهاية العقد
                                </label>
                                {{ form.contract_end_date }}
                                {% if form.contract_end_date.errors %}
                                <div class="text-danger small">{{ form.contract_end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    حالة الموظف <span class="text-danger">*</span>
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate employee number if empty
    const employeeNumberField = document.getElementById('{{ form.employee_number.id_for_label }}');
    const personField = document.getElementById('{{ form.person.id_for_label }}');
    
    if (employeeNumberField && employeeNumberField.value === '') {
        // Generate a simple employee number based on current timestamp
        const now = new Date();
        const empNumber = 'EMP' + now.getFullYear().toString().substr(-2) + 
                         String(now.getMonth() + 1).padStart(2, '0') + 
                         String(now.getDate()).padStart(2, '0') + 
                         String(Math.floor(Math.random() * 100)).padStart(2, '0');
        employeeNumberField.value = empNumber;
    }
    
    // Update position options based on selected department
    const departmentField = document.getElementById('{{ form.department.id_for_label }}');
    const positionField = document.getElementById('{{ form.position.id_for_label }}');
    
    if (departmentField && positionField) {
        departmentField.addEventListener('change', function() {
            const departmentId = this.value;
            if (departmentId) {
                // Here you could add AJAX call to filter positions by department
                // For now, we'll keep all positions available
            }
        });
    }
});
</script>
{% endblock %}
