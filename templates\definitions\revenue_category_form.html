{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-coins me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:revenue_category_list' %}">فئات الإيرادات</a></li>
                <li class="breadcrumb-item active">{{ action }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if revenue_category %}edit{% else %}plus{% endif %} me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <!-- Basic Information -->
                    <h6 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                {{ form.category.label }}
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.category.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Accounting Settings -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-calculator me-2"></i>
                        الإعدادات المحاسبية
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.default_account.id_for_label }}" class="form-label">
                                {{ form.default_account.label }}
                            </label>
                            {{ form.default_account }}
                            {% if form.default_account.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.default_account.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Tax Settings -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-percentage me-2"></i>
                        إعدادات الضرائب
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_taxable }}
                                <label class="form-check-label" for="{{ form.is_taxable.id_for_label }}">
                                    {{ form.is_taxable.label }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.tax_rate.id_for_label }}" class="form-label">
                                {{ form.tax_rate.label }}
                            </label>
                            <div class="input-group">
                                {{ form.tax_rate }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.tax_rate.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.tax_rate.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ action }}
                            </button>
                            <a href="{% url 'definitions:revenue_category_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if revenue_category %}
                            <button type="button" 
                                    class="btn btn-outline-danger delete-btn" 
                                    data-id="{{ revenue_category.pk }}"
                                    data-name="{{ revenue_category.name }}">
                                <i class="fas fa-trash me-2"></i>
                                حذف
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
        
        {% if revenue_category %}
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ revenue_category.created_at|date:"d/m/Y H:i" }}</p>
                        {% if revenue_category.created_by %}
                            <p><strong>أنشئ بواسطة:</strong> {{ revenue_category.created_by.get_full_name|default:revenue_category.created_by.username }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if revenue_category.updated_at %}
                            <p><strong>تاريخ التحديث:</strong> {{ revenue_category.updated_at|date:"d/m/Y H:i" }}</p>
                        {% endif %}
                        {% if revenue_category.updated_by %}
                            <p><strong>حُدث بواسطة:</strong> {{ revenue_category.updated_by.get_full_name|default:revenue_category.updated_by.username }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if revenue_category %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف فئة الإيراد: <strong>{{ revenue_category.name }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase revenue category code
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    if (codeField) {
        codeField.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    // Toggle tax rate field based on is_taxable checkbox
    const isTaxableField = document.getElementById('{{ form.is_taxable.id_for_label }}');
    const taxRateField = document.getElementById('{{ form.tax_rate.id_for_label }}');
    
    function toggleTaxRate() {
        if (isTaxableField.checked) {
            taxRateField.disabled = false;
            taxRateField.parentElement.parentElement.style.opacity = '1';
        } else {
            taxRateField.disabled = true;
            taxRateField.value = '';
            taxRateField.parentElement.parentElement.style.opacity = '0.5';
        }
    }
    
    if (isTaxableField && taxRateField) {
        isTaxableField.addEventListener('change', toggleTaxRate);
        toggleTaxRate(); // Initial state
    }
    
    {% if revenue_category %}
    // Delete functionality
    const deleteBtn = document.querySelector('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete');

    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteModal.show();
        });
    }

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            fetch(`/definitions/revenue-categories/{{ revenue_category.pk }}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{% url "definitions:revenue_category_list" %}';
                } else {
                    alert(data.message || 'حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
            });
            deleteModal.hide();
        });
    }
    {% endif %}
});
</script>
{% endblock %}
