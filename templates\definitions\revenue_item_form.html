{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-hand-holding-usd me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:revenue_item_list' %}">بنود الإيرادات</a></li>
                <li class="breadcrumb-item active">{{ action }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if revenue_item %}edit{% else %}plus{% endif %} me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <!-- Basic Information -->
                    <h6 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h6>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-5 mb-3">
                            <label for="{{ form.revenue_category.id_for_label }}" class="form-label">
                                {{ form.revenue_category.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.revenue_category }}
                            {% if form.revenue_category.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.revenue_category.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Accounting Settings -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-calculator me-2"></i>
                        الإعدادات المحاسبية
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.account_number.id_for_label }}" class="form-label">
                                {{ form.account_number.label }}
                            </label>
                            {{ form.account_number }}
                            {% if form.account_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.account_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Recurring Settings -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-sync me-2"></i>
                        إعدادات التكرار
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_recurring }}
                                <label class="form-check-label" for="{{ form.is_recurring.id_for_label }}">
                                    {{ form.is_recurring.label }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.recurring_period.id_for_label }}" class="form-label">
                                {{ form.recurring_period.label }}
                            </label>
                            {{ form.recurring_period }}
                            {% if form.recurring_period.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.recurring_period.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Tax and Commission Settings -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-percentage me-2"></i>
                        إعدادات الضرائب والعمولات
                    </h6>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                {{ form.is_taxable }}
                                <label class="form-check-label" for="{{ form.is_taxable.id_for_label }}">
                                    {{ form.is_taxable.label }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.tax_rate.id_for_label }}" class="form-label">
                                {{ form.tax_rate.label }}
                            </label>
                            <div class="input-group">
                                {{ form.tax_rate }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.tax_rate.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.tax_rate.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.commission_rate.id_for_label }}" class="form-label">
                                {{ form.commission_rate.label }}
                            </label>
                            <div class="input-group">
                                {{ form.commission_rate }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.commission_rate.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.commission_rate.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                {{ form.requires_contract }}
                                <label class="form-check-label" for="{{ form.requires_contract.id_for_label }}">
                                    {{ form.requires_contract.label }}
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Amount Limits -->
                    <h6 class="mb-3 mt-4">
                        <i class="fas fa-dollar-sign me-2"></i>
                        حدود المبلغ
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.min_amount.id_for_label }}" class="form-label">
                                {{ form.min_amount.label }}
                            </label>
                            {{ form.min_amount }}
                            {% if form.min_amount.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.min_amount.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.max_amount.id_for_label }}" class="form-label">
                                {{ form.max_amount.label }}
                            </label>
                            {{ form.max_amount }}
                            {% if form.max_amount.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.max_amount.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ action }}
                            </button>
                            <a href="{% url 'definitions:revenue_item_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if revenue_item %}
                            <button type="button" 
                                    class="btn btn-outline-danger delete-btn" 
                                    data-id="{{ revenue_item.pk }}"
                                    data-name="{{ revenue_item.name }}">
                                <i class="fas fa-trash me-2"></i>
                                حذف
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase revenue item code
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    if (codeField) {
        codeField.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    // Toggle recurring period field based on is_recurring checkbox
    const isRecurringField = document.getElementById('{{ form.is_recurring.id_for_label }}');
    const recurringPeriodField = document.getElementById('{{ form.recurring_period.id_for_label }}');
    
    function toggleRecurringPeriod() {
        if (isRecurringField.checked) {
            recurringPeriodField.disabled = false;
            recurringPeriodField.parentElement.style.opacity = '1';
        } else {
            recurringPeriodField.disabled = true;
            recurringPeriodField.value = '';
            recurringPeriodField.parentElement.style.opacity = '0.5';
        }
    }
    
    // Toggle tax rate field based on is_taxable checkbox
    const isTaxableField = document.getElementById('{{ form.is_taxable.id_for_label }}');
    const taxRateField = document.getElementById('{{ form.tax_rate.id_for_label }}');
    
    function toggleTaxRate() {
        if (isTaxableField.checked) {
            taxRateField.disabled = false;
            taxRateField.parentElement.parentElement.style.opacity = '1';
        } else {
            taxRateField.disabled = true;
            taxRateField.value = '';
            taxRateField.parentElement.parentElement.style.opacity = '0.5';
        }
    }
    
    if (isRecurringField && recurringPeriodField) {
        isRecurringField.addEventListener('change', toggleRecurringPeriod);
        toggleRecurringPeriod(); // Initial state
    }
    
    if (isTaxableField && taxRateField) {
        isTaxableField.addEventListener('change', toggleTaxRate);
        toggleTaxRate(); // Initial state
    }
});
</script>
{% endblock %}
