{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }

    .settings-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .setting-item {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-left: 4px solid #0d6efd;
        transition: all 0.3s ease;
    }

    .setting-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }

    .password-strength {
        height: 4px;
        border-radius: 2px;
        margin-top: 5px;
        transition: all 0.3s ease;
    }

    .strength-weak { background: #dc3545; }
    .strength-medium { background: #ffc107; }
    .strength-strong { background: #198754; }

    .tab-content {
        padding: 20px 0;
    }

    .nav-pills .nav-link {
        border-radius: 25px;
        padding: 10px 20px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-0">
                        <i class="fas fa-cog text-primary me-3"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0 mt-2">إدارة المستخدمين وإعدادات النظام</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6 p-3">
                        <i class="fas fa-users me-2"></i>
                        {{ total_users }} مستخدم | {{ total_staff }} مدير
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills justify-content-center" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="users-tab" data-bs-toggle="pill" data-bs-target="#users" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="password-tab" data-bs-toggle="pill" data-bs-target="#password" type="button" role="tab">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="pill" data-bs-target="#settings" type="button" role="tab">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="permissions-tab" data-bs-toggle="pill" data-bs-target="#permissions" type="button" role="tab">
                        <i class="fas fa-shield-alt me-2"></i>
                        الصلاحيات
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
    <div class="row mb-3">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                {% if message.tags == 'error' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% elif message.tags == 'success' %}
                    <i class="fas fa-check-circle me-2"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% else %}
                    <i class="fas fa-info-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Tab Content -->
    <div class="tab-content" id="settingsTabContent">
        <!-- إدارة المستخدمين -->
        <div class="tab-pane fade show active" id="users" role="tabpanel">
            <div class="row">
                <!-- إضافة مستخدم جديد -->
                <div class="col-lg-4 mb-4">
                    <div class="card settings-card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مستخدم جديد
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="create_user">

                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم:</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني:</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">الاسم الأول:</label>
                                            <input type="text" class="form-control" name="first_name">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">الاسم الأخير:</label>
                                            <input type="text" class="form-control" name="last_name">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور:</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="is_staff" id="is_staff">
                                    <label class="form-check-label" for="is_staff">
                                        مدير النظام
                                    </label>
                                </div>

                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة المستخدم
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- قائمة المستخدمين -->
                <div class="col-lg-8 mb-4">
                    <div class="card settings-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                المستخدمين الحاليين ({{ total_users }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if users %}
                                <div class="row">
                                    {% for user in users %}
                                    <div class="col-md-6 mb-3">
                                        <div class="d-flex align-items-center p-3 border rounded">
                                            <div class="user-avatar me-3">
                                                {% if user.first_name %}
                                                    {{ user.first_name|first }}{{ user.last_name|first }}
                                                {% else %}
                                                    {{ user.username|first|upper }}
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                                <small class="text-muted">{{ user.email }}</small>
                                                <div class="mt-1">
                                                    {% if user.is_superuser %}
                                                        <span class="badge bg-danger">مدير عام</span>
                                                    {% elif user.is_staff %}
                                                        <span class="badge bg-warning">مدير</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">مستخدم</span>
                                                    {% endif %}

                                                    {% if user.is_active %}
                                                        <span class="badge bg-success">نشط</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">معطل</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="editUser({{ user.id }}, '{{ user.username }}', '{{ user.email }}', '{{ user.first_name }}', '{{ user.last_name }}', {{ user.is_staff|yesno:'true,false' }})">
                                                            <i class="fas fa-edit me-2"></i>تعديل
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="resetPassword({{ user.id }}, '{{ user.username }}')">
                                                            <i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    {% if user != request.user %}
                                                    <li>
                                                        <form method="POST" style="display: inline;">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="action" value="toggle_user_status">
                                                            <input type="hidden" name="user_id" value="{{ user.id }}">
                                                            <button type="submit" class="dropdown-item {% if user.is_active %}text-danger{% else %}text-success{% endif %}"
                                                                    onclick="return confirm('هل أنت متأكد؟')">
                                                                <i class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %} me-2"></i>
                                                                {% if user.is_active %}تعطيل{% else %}تفعيل{% endif %}
                                                            </button>
                                                        </form>
                                                    </li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا يوجد مستخدمين</h5>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- تغيير كلمة المرور -->
        <div class="tab-pane fade" id="password" role="tabpanel">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card settings-card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="passwordForm">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="change_password">

                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الحالية:</label>
                                    <input type="password" class="form-control" name="old_password" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الجديدة:</label>
                                    <input type="password" class="form-control" name="new_password" id="newPassword" required>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <small class="text-muted">يجب أن تحتوي على 8 أحرف على الأقل</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور:</label>
                                    <input type="password" class="form-control" name="confirm_password" id="confirmPassword" required>
                                    <div id="passwordMatch" class="mt-1"></div>
                                </div>

                                <button type="submit" class="btn btn-warning w-100" id="changePasswordBtn" disabled>
                                    <i class="fas fa-save me-2"></i>
                                    تغيير كلمة المرور
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="tab-pane fade" id="settings" role="tabpanel">
            <div class="row">
                {% for category, settings in settings_by_category.items %}
                <div class="col-lg-6 mb-4">
                    <div class="card settings-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>
                                {{ category }}
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for setting in settings %}
                            <div class="setting-item">
                                <form method="POST" class="setting-form">
                                    {% csrf_token %}
                                    <input type="hidden" name="action" value="update_setting">
                                    <input type="hidden" name="setting_key" value="{{ setting.key }}">

                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1 me-3">
                                            <h6 class="mb-1">{{ setting.description|default:setting.key }}</h6>
                                            <small class="text-muted">{{ setting.key }}</small>
                                        </div>
                                        <div style="min-width: 150px;">
                                            {% if setting.value_type == 'BOOLEAN' %}
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" name="setting_value"
                                                           value="true" {% if setting.get_value %}checked{% endif %}
                                                           {% if not setting.is_editable %}disabled{% endif %}
                                                           onchange="submitSettingForm(this)">
                                                </div>
                                            {% elif setting.value_type == 'INTEGER' %}
                                                <input type="number" class="form-control form-control-sm"
                                                       name="setting_value" value="{{ setting.value }}"
                                                       {% if not setting.is_editable %}readonly{% endif %}
                                                       onchange="this.form.submit()">
                                            {% elif setting.value_type == 'FLOAT' %}
                                                <input type="number" step="0.01" class="form-control form-control-sm"
                                                       name="setting_value" value="{{ setting.value }}"
                                                       {% if not setting.is_editable %}readonly{% endif %}
                                                       onchange="this.form.submit()">
                                            {% else %}
                                                <input type="text" class="form-control form-control-sm"
                                                       name="setting_value" value="{{ setting.value }}"
                                                       {% if not setting.is_editable %}readonly{% endif %}
                                                       onchange="this.form.submit()">
                                            {% endif %}
                                        </div>
                                    </div>

                                    {% if not setting.is_editable %}
                                    <small class="text-warning">
                                        <i class="fas fa-lock me-1"></i>
                                        إعداد نظام غير قابل للتعديل
                                    </small>
                                    {% endif %}
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إعدادات</h5>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- الصلاحيات -->
        <div class="tab-pane fade" id="permissions" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card settings-card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                إدارة الصلاحيات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- صلاحيات المبيعات -->
                                <div class="col-lg-4 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-shopping-cart me-2"></i>
                                                المبيعات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sales_view">
                                                <label class="form-check-label" for="sales_view">عرض</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sales_add">
                                                <label class="form-check-label" for="sales_add">إضافة</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sales_edit">
                                                <label class="form-check-label" for="sales_edit">تعديل</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sales_delete">
                                                <label class="form-check-label" for="sales_delete">حذف</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- صلاحيات المشتريات -->
                                <div class="col-lg-4 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-shopping-bag me-2"></i>
                                                المشتريات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="purchases_view">
                                                <label class="form-check-label" for="purchases_view">عرض</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="purchases_add">
                                                <label class="form-check-label" for="purchases_add">إضافة</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="purchases_edit">
                                                <label class="form-check-label" for="purchases_edit">تعديل</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="purchases_delete">
                                                <label class="form-check-label" for="purchases_delete">حذف</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- صلاحيات التقارير -->
                                <div class="col-lg-4 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-warning text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-chart-bar me-2"></i>
                                                التقارير
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="reports_view">
                                                <label class="form-check-label" for="reports_view">عرض</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="reports_export">
                                                <label class="form-check-label" for="reports_export">تصدير</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="reports_print">
                                                <label class="form-check-label" for="reports_print">طباعة</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="reports_advanced">
                                                <label class="form-check-label" for="reports_advanced">تقارير متقدمة</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button class="btn btn-danger">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الصلاحيات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتعديل المستخدم -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editUserForm">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit_user">
                <input type="hidden" name="user_id" id="editUserId">

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم:</label>
                        <input type="text" class="form-control" name="username" id="editUsername" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني:</label>
                        <input type="email" class="form-control" name="email" id="editEmail" required>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأول:</label>
                                <input type="text" class="form-control" name="first_name" id="editFirstName">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأخير:</label>
                                <input type="text" class="form-control" name="last_name" id="editLastName">
                            </div>
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_staff" id="editIsStaff">
                        <label class="form-check-label" for="editIsStaff">
                            مدير النظام
                        </label>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal لإعادة تعيين كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                {% csrf_token %}
                <input type="hidden" name="action" value="reset_password">
                <input type="hidden" name="user_id" id="resetUserId">

                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم إعادة تعيين كلمة مرور المستخدم <strong id="resetUsername"></strong>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور الجديدة:</label>
                        <input type="password" class="form-control" name="new_password" value="12345678" required>
                        <small class="text-muted">كلمة المرور الافتراضية: 12345678</small>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">إعادة تعيين</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password strength checker
    const newPassword = document.getElementById('newPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const passwordStrength = document.getElementById('passwordStrength');
    const passwordMatch = document.getElementById('passwordMatch');
    const changePasswordBtn = document.getElementById('changePasswordBtn');

    function checkPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        passwordStrength.className = 'password-strength ';
        if (strength < 2) {
            passwordStrength.classList.add('strength-weak');
        } else if (strength < 4) {
            passwordStrength.classList.add('strength-medium');
        } else {
            passwordStrength.classList.add('strength-strong');
        }
    }

    function checkPasswordMatch() {
        if (confirmPassword.value === '') {
            passwordMatch.innerHTML = '';
            return false;
        }

        if (newPassword.value === confirmPassword.value) {
            passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>كلمات المرور متطابقة</small>';
            return true;
        } else {
            passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>كلمات المرور غير متطابقة</small>';
            return false;
        }
    }

    function updatePasswordButton() {
        const isValid = newPassword.value.length >= 8 && checkPasswordMatch();
        changePasswordBtn.disabled = !isValid;
    }

    if (newPassword) {
        newPassword.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            updatePasswordButton();
        });
    }

    if (confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            checkPasswordMatch();
            updatePasswordButton();
        });
    }

    // Auto-submit setting forms with delay
    const settingForms = document.querySelectorAll('.setting-form');
    settingForms.forEach(form => {
        const inputs = form.querySelectorAll('input[type="text"], input[type="number"]');
        inputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    form.submit();
                }, 1000); // Submit after 1 second of no typing
            });
        });
    });

    // Functions for user management
    window.editUser = function(id, username, email, firstName, lastName, isStaff) {
        document.getElementById('editUserId').value = id;
        document.getElementById('editUsername').value = username;
        document.getElementById('editEmail').value = email;
        document.getElementById('editFirstName').value = firstName;
        document.getElementById('editLastName').value = lastName;
        document.getElementById('editIsStaff').checked = isStaff;

        new bootstrap.Modal(document.getElementById('editUserModal')).show();
    };

    window.resetPassword = function(id, username) {
        document.getElementById('resetUserId').value = id;
        document.getElementById('resetUsername').textContent = username;

        new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
    };

    // Function to handle boolean settings
    window.submitSettingForm = function(checkbox) {
        const form = checkbox.closest('form');
        if (!checkbox.checked) {
            // Add hidden input for false value
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'setting_value';
            hiddenInput.value = 'false';
            form.appendChild(hiddenInput);
        }
        form.submit();
    };
});
</script>
{% endblock %}
