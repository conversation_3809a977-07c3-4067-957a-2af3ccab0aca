{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-hand-holding-usd text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:treasury_reports' %}">تقارير الخزينة</a></li>
                            <li class="breadcrumb-item active">التحصيلات النقدية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">العميل</label>
                    <select name="customer_id" class="form-select">
                        <option value="">جميع العملاء</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}" {% if selected_customer == customer.id|stringformat:"s" %}selected{% endif %}>
                            {{ customer.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if collections %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي التحصيلات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_collections }}</h4>
                            <p class="mb-0">عدد التحصيلات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ average_collection|floatformat:2 }}</h4>
                            <p class="mb-0">متوسط التحصيل</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collections -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل التحصيلات النقدية
                <span class="badge bg-success">{{ total_collections }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم الإيصال</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>البيان</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for collection in collections %}
                        <tr>
                            <td>{{ collection.transaction_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong class="text-primary">{{ collection.receipt_number|default:collection.id }}</strong>
                            </td>
                            <td>
                                <strong>{{ collection.customer.name }}</strong>
                                {% if collection.customer.phone %}
                                <small class="text-muted d-block">{{ collection.customer.phone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-success">{{ collection.amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if collection.payment_method == 'CASH' %}
                                <span class="badge bg-success">نقدي</span>
                                {% elif collection.payment_method == 'BANK' %}
                                <span class="badge bg-primary">بنكي</span>
                                {% elif collection.payment_method == 'CHECK' %}
                                <span class="badge bg-info">شيك</span>
                                {% elif collection.payment_method == 'CREDIT_CARD' %}
                                <span class="badge bg-warning">بطاقة ائتمان</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ collection.payment_method }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if collection.status == 'PENDING' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif collection.status == 'CONFIRMED' %}
                                <span class="badge bg-success">مؤكد</span>
                                {% elif collection.status == 'COLLECTED' %}
                                <span class="badge bg-primary">محصل</span>
                                {% elif collection.status == 'CANCELLED' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ collection.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if collection.description %}
                                {{ collection.description|truncatechars:30 }}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if collection.notes %}
                                <small class="text-muted">{{ collection.notes|truncatechars:30 }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th class="text-success">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="4"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تحصيلات نقدية</h5>
            <p class="text-muted">لا توجد تحصيلات نقدية في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
