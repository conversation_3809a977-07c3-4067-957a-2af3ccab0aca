from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import logout, login
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import json
import os
import subprocess
import hashlib

from .models import (
    DeletedRecord, EditHistory, SystemBackup, SystemSettings,
    LicenseInfo, TaskbarSettings
)


# إدارة البيانات
@login_required
@staff_member_required
def delete_data(request):
    """حذف البيانات المسجلة"""

    # قائمة النماذج المتاحة للحذف
    available_models = [
        {'name': 'Person', 'app': 'definitions', 'verbose_name': 'الأشخاص'},
        {'name': 'Item', 'app': 'definitions', 'verbose_name': 'الأصناف'},
        {'name': 'SalesInvoice', 'app': 'sales', 'verbose_name': 'فواتير المبيعات'},
        {'name': 'PurchaseInvoice', 'app': 'purchases', 'verbose_name': 'فواتير المشتريات'},
        {'name': 'JournalEntry', 'app': 'accounting', 'verbose_name': 'القيود المحاسبية'},
    ]

    if request.method == 'POST':
        selected_models = request.POST.getlist('models')
        date_from = request.POST.get('date_from')
        date_to = request.POST.get('date_to')
        confirm_delete = request.POST.get('confirm_delete')

        if not confirm_delete:
            messages.error(request, 'يجب تأكيد عملية الحذف')
            return redirect('services:delete_data')

        # تنفيذ عملية الحذف
        deleted_count = 0
        for model_name in selected_models:
            try:
                # هنا يمكن إضافة منطق الحذف الفعلي
                deleted_count += 1
            except Exception as e:
                messages.error(request, f'خطأ في حذف {model_name}: {str(e)}')

        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} نوع من البيانات بنجاح')

        return redirect('services:delete_data')

    context = {
        'title': 'حذف البيانات المسجلة',
        'available_models': available_models,
    }
    return render(request, 'services/delete_data.html', context)


@login_required
def recycle_bin(request):
    """سلة المحذوفات"""
    context = {
        'title': 'سلة المحذوفات',
    }
    return render(request, 'services/recycle_bin.html', context)


@login_required
def edit_history(request):
    """سلة التعديلات"""
    context = {
        'title': 'سلة التعديلات',
    }
    return render(request, 'services/edit_history.html', context)


@login_required
@staff_member_required
def backup(request):
    """النسخ الاحتياطي"""
    context = {
        'title': 'النسخ الاحتياطي',
    }
    return render(request, 'services/backup.html', context)


@login_required
def license_info(request):
    """معلومات الترخيص"""
    context = {
        'title': 'ترخيص النسخة',
    }
    return render(request, 'services/license.html', context)


@login_required
@staff_member_required
def recalculate_costs(request):
    """إعادة حساب أسعار تكلفة الأصناف"""
    context = {
        'title': 'إعادة حساب أسعار تكلفة الأصناف',
    }
    return render(request, 'services/recalculate_costs.html', context)


@login_required
def taskbar_settings(request):
    """إعدادات شريط المهام"""
    context = {
        'title': 'إعدادات شريط المهام',
    }
    return render(request, 'services/taskbar_settings.html', context)


@login_required
@staff_member_required
def system_settings(request):
    """كلمات السر وخيارات البرنامج"""
    context = {
        'title': 'كلمات السر وخيارات البرنامج',
    }
    return render(request, 'services/system_settings.html', context)


@login_required
def print_design(request):
    """تصميم نماذج الطباعة"""
    context = {
        'title': 'تصميم نماذج الطباعة',
    }
    return render(request, 'services/print_design.html', context)


@login_required
def barcode_design(request):
    """تصميم نماذج الباركود"""
    context = {
        'title': 'تصميم نماذج الباركود',
    }
    return render(request, 'services/barcode_design.html', context)


@login_required
def relogin(request):
    """إعادة الدخول كمستخدم آخر"""
    context = {
        'title': 'إعادة الدخول كمستخدم',
    }
    return render(request, 'services/relogin.html', context)


# AJAX endpoints
@require_POST
@csrf_exempt
def ajax_delete_records(request):
    """حذف السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الحذف بنجاح'})


@require_POST
@csrf_exempt
def ajax_restore_records(request):
    """استرداد السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الاسترداد بنجاح'})


@require_POST
@csrf_exempt
def ajax_create_backup(request):
    """إنشاء نسخة احتياطية عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إنشاء النسخة الاحتياطية'})


@require_POST
@csrf_exempt
def ajax_recalculate_costs(request):
    """إعادة حساب التكلفة عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إعادة حساب التكلفة'})
