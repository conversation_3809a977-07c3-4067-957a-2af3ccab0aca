from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import logout, login
from django.contrib.auth.models import User
import json
from django.utils import timezone
from datetime import datetime, timedelta
import json
import os
import subprocess
import hashlib

from .models import (
    DeletedRecord, EditHistory, SystemBackup, SystemSettings,
    LicenseInfo, TaskbarSettings
)


# لوحة تحكم الخدمات
@login_required
def services_dashboard(request):
    """لوحة تحكم الخدمات"""

    # إحصائيات سريعة
    total_deleted_records = DeletedRecord.objects.filter(is_restored=False).count()
    total_edit_history = EditHistory.objects.count()
    total_backups = SystemBackup.objects.filter(is_valid=True).count()
    total_settings = SystemSettings.objects.count()

    # آخر العمليات
    recent_deleted = DeletedRecord.objects.filter(is_restored=False).order_by('-deleted_at')[:5]
    recent_edits = EditHistory.objects.order_by('-edited_at')[:5]
    recent_backups = SystemBackup.objects.filter(is_valid=True).order_by('-created_at')[:3]

    # معلومات الترخيص
    try:
        license_info = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_info = None

    # إعدادات شريط المهام للمستخدم الحالي
    try:
        taskbar_settings = TaskbarSettings.objects.get(user=request.user)
    except TaskbarSettings.DoesNotExist:
        taskbar_settings = None

    # إحصائيات النظام
    total_users = User.objects.filter(is_active=True).count()
    total_staff = User.objects.filter(is_staff=True, is_active=True).count()

    # حجم النسخ الاحتياطية
    total_backup_size = SystemBackup.objects.filter(is_valid=True).aggregate(
        total_size=Sum('file_size'))['total_size'] or 0

    # إعدادات النظام حسب الفئة
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = 0
        settings_by_category[setting.category] += 1

    context = {
        'title': 'لوحة تحكم الخدمات',
        'total_deleted_records': total_deleted_records,
        'total_edit_history': total_edit_history,
        'total_backups': total_backups,
        'total_settings': total_settings,
        'recent_deleted': recent_deleted,
        'recent_edits': recent_edits,
        'recent_backups': recent_backups,
        'license': license_info,  # تغيير الاسم ليتطابق مع القالب
        'taskbar_settings': taskbar_settings,
        'total_users': total_users,
        'total_staff': total_staff,
        'total_backup_size': total_backup_size,
        'settings_by_category': settings_by_category,
    }
    return render(request, 'services/dashboard.html', context)


# إدارة البيانات
@login_required
@staff_member_required
def delete_data(request):
    """حذف البيانات المسجلة"""

    # قائمة النماذج المتاحة للحذف
    available_models = [
        {'name': 'Person', 'app': 'definitions', 'verbose_name': 'الأشخاص'},
        {'name': 'Item', 'app': 'definitions', 'verbose_name': 'الأصناف'},
        {'name': 'SalesInvoice', 'app': 'sales', 'verbose_name': 'فواتير المبيعات'},
        {'name': 'PurchaseInvoice', 'app': 'purchases', 'verbose_name': 'فواتير المشتريات'},
        {'name': 'JournalEntry', 'app': 'accounting', 'verbose_name': 'القيود المحاسبية'},
    ]

    if request.method == 'POST':
        selected_models = request.POST.getlist('models')
        date_from = request.POST.get('date_from')
        date_to = request.POST.get('date_to')
        confirm_delete = request.POST.get('confirm_delete')

        if not confirm_delete:
            messages.error(request, 'يجب تأكيد عملية الحذف')
            return redirect('services:delete_data')

        # تنفيذ عملية الحذف
        deleted_count = 0
        for model_name in selected_models:
            try:
                # هنا يمكن إضافة منطق الحذف الفعلي
                deleted_count += 1
            except Exception as e:
                messages.error(request, f'خطأ في حذف {model_name}: {str(e)}')

        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} نوع من البيانات بنجاح')

        return redirect('services:delete_data')

    context = {
        'title': 'حذف البيانات المسجلة',
        'available_models': available_models,
    }
    return render(request, 'services/delete_data.html', context)


@login_required
def recycle_bin(request):
    """سلة المحذوفات"""
    context = {
        'title': 'سلة المحذوفات',
    }
    return render(request, 'services/recycle_bin.html', context)


@login_required
def edit_history(request):
    """سلة التعديلات"""
    context = {
        'title': 'سلة التعديلات',
    }
    return render(request, 'services/edit_history.html', context)


@login_required
@staff_member_required
def backup(request):
    """النسخ الاحتياطي"""
    context = {
        'title': 'النسخ الاحتياطي',
    }
    return render(request, 'services/backup.html', context)


@login_required
def license_info(request):
    """معلومات الترخيص"""
    try:
        license_obj = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_obj = None

    context = {
        'title': 'ترخيص النسخة',
        'license': license_obj,
    }
    return render(request, 'services/license.html', context)


@login_required
@staff_member_required
def recalculate_costs(request):
    """إعادة حساب أسعار تكلفة الأصناف"""
    context = {
        'title': 'إعادة حساب أسعار تكلفة الأصناف',
    }
    return render(request, 'services/recalculate_costs.html', context)


@login_required
def taskbar_settings(request):
    """إعدادات شريط المهام"""

    try:
        settings = TaskbarSettings.objects.get(user=request.user)
    except TaskbarSettings.DoesNotExist:
        settings = TaskbarSettings.objects.create(user=request.user)

    if request.method == 'POST':
        settings.position = request.POST.get('position', 'HORIZONTAL')
        settings.auto_hide = request.POST.get('auto_hide') == 'on'
        settings.show_icons = request.POST.get('show_icons') == 'on'
        settings.show_text = request.POST.get('show_text') == 'on'
        settings.theme = request.POST.get('theme', 'default')
        settings.size = request.POST.get('size', 'medium')

        # ترتيب القوائم
        menu_order = request.POST.get('menu_order', '[]')
        try:
            settings.menu_order = json.loads(menu_order)
        except:
            settings.menu_order = []

        # العناصر المثبتة
        pinned_items = request.POST.getlist('pinned_items')
        settings.pinned_items = pinned_items

        settings.save()
        messages.success(request, 'تم حفظ إعدادات شريط المهام بنجاح')
        return redirect('services:taskbar_settings')

    context = {
        'title': 'إعدادات شريط المهام',
        'settings': settings,
    }
    return render(request, 'services/taskbar_settings.html', context)


@login_required
@staff_member_required
def system_settings(request):
    """كلمات السر وخيارات البرنامج"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'change_password':
            # تغيير كلمة المرور
            old_password = request.POST.get('old_password')
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')

            if not request.user.check_password(old_password):
                messages.error(request, 'كلمة المرور الحالية غير صحيحة')
            elif new_password != confirm_password:
                messages.error(request, 'كلمة المرور الجديدة غير متطابقة')
            elif len(new_password) < 8:
                messages.error(request, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
            else:
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, 'تم تغيير كلمة المرور بنجاح')
                return redirect('services:system_settings')

        elif action == 'update_setting':
            # تحديث إعداد معين
            setting_key = request.POST.get('setting_key')
            setting_value = request.POST.get('setting_value')

            try:
                setting = SystemSettings.objects.get(key=setting_key)
                if setting.is_editable:
                    # التحقق من صحة البيانات حسب النوع
                    if setting.value_type == 'BOOLEAN':
                        setting_value = 'true' if setting_value == 'true' else 'false'
                    elif setting.value_type == 'INTEGER':
                        try:
                            int(setting_value)
                        except ValueError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم صحيح'})
                            messages.error(request, 'يجب أن تكون القيمة رقم صحيح')
                            return redirect('services:system_settings')
                    elif setting.value_type == 'FLOAT':
                        try:
                            float(setting_value)
                        except ValueError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم عشري'})
                            messages.error(request, 'يجب أن تكون القيمة رقم عشري')
                            return redirect('services:system_settings')
                    elif setting.value_type == 'JSON':
                        try:
                            json.loads(setting_value)
                        except json.JSONDecodeError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'صيغة JSON غير صحيحة'})
                            messages.error(request, 'صيغة JSON غير صحيحة')
                            return redirect('services:system_settings')

                    setting.value = setting_value
                    setting.updated_by = request.user
                    setting.save()

                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'success': True, 'message': f'تم تحديث إعداد {setting.description} بنجاح'})
                    messages.success(request, f'تم تحديث إعداد {setting.description} بنجاح')
                else:
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'success': False, 'message': 'هذا الإعداد غير قابل للتعديل'})
                    messages.error(request, 'هذا الإعداد غير قابل للتعديل')
            except SystemSettings.DoesNotExist:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'success': False, 'message': 'الإعداد غير موجود'})
                messages.error(request, 'الإعداد غير موجود')

        elif action == 'create_user':
            # إنشاء مستخدم جديد
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            password = request.POST.get('password')
            is_staff = request.POST.get('is_staff') == 'on'

            if User.objects.filter(username=username).exists():
                messages.error(request, 'اسم المستخدم موجود بالفعل')
            elif User.objects.filter(email=email).exists():
                messages.error(request, 'البريد الإلكتروني موجود بالفعل')
            else:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    password=password,
                    is_staff=is_staff
                )
                messages.success(request, f'تم إنشاء المستخدم {username} بنجاح')

        elif action == 'toggle_user_status':
            # تفعيل/تعطيل مستخدم
            user_id = request.POST.get('user_id')
            try:
                user = User.objects.get(id=user_id)
                if user != request.user:  # لا يمكن تعطيل النفس
                    user.is_active = not user.is_active
                    user.save()
                    status = 'تم تفعيل' if user.is_active else 'تم تعطيل'
                    messages.success(request, f'{status} المستخدم {user.username} بنجاح')
                else:
                    messages.error(request, 'لا يمكنك تعطيل حسابك الخاص')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        elif action == 'reset_password':
            # إعادة تعيين كلمة مرور مستخدم
            user_id = request.POST.get('user_id')
            new_password = request.POST.get('new_password', '12345678')
            try:
                user = User.objects.get(id=user_id)
                user.set_password(new_password)
                user.save()
                messages.success(request, f'تم إعادة تعيين كلمة مرور المستخدم {user.username}')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        elif action == 'edit_user':
            # تعديل بيانات مستخدم
            user_id = request.POST.get('user_id')
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            is_staff = request.POST.get('is_staff') == 'on'

            try:
                user = User.objects.get(id=user_id)

                # التحقق من عدم تكرار اسم المستخدم
                if User.objects.filter(username=username).exclude(id=user_id).exists():
                    messages.error(request, 'اسم المستخدم موجود بالفعل')
                elif User.objects.filter(email=email).exclude(id=user_id).exists():
                    messages.error(request, 'البريد الإلكتروني موجود بالفعل')
                else:
                    user.username = username
                    user.email = email
                    user.first_name = first_name
                    user.last_name = last_name
                    user.is_staff = is_staff
                    user.save()
                    messages.success(request, f'تم تحديث بيانات المستخدم {username} بنجاح')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

    # جلب البيانات للعرض
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = []
        settings_by_category[setting.category].append(setting)

    users = User.objects.all().order_by('username')
    staff_users = User.objects.filter(is_staff=True).order_by('username')

    context = {
        'title': 'كلمات السر وخيارات البرنامج',
        'settings_by_category': settings_by_category,
        'users': users,
        'staff_users': staff_users,
        'total_users': users.count(),
        'total_staff': staff_users.count(),
    }
    return render(request, 'services/system_settings.html', context)


@login_required
def print_design(request):
    """تصميم نماذج الطباعة"""
    context = {
        'title': 'تصميم نماذج الطباعة',
    }
    return render(request, 'services/print_design.html', context)


@login_required
def barcode_design(request):
    """تصميم نماذج الباركود"""
    context = {
        'title': 'تصميم نماذج الباركود',
    }
    return render(request, 'services/barcode_design.html', context)


@login_required
def relogin(request):
    """إعادة الدخول كمستخدم آخر"""
    context = {
        'title': 'إعادة الدخول كمستخدم',
    }
    return render(request, 'services/relogin.html', context)


# AJAX endpoints
@require_POST
@csrf_exempt
def ajax_delete_records(request):
    """حذف السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الحذف بنجاح'})


@require_POST
@csrf_exempt
def ajax_restore_records(request):
    """استرداد السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الاسترداد بنجاح'})


@require_POST
@csrf_exempt
def ajax_create_backup(request):
    """إنشاء نسخة احتياطية عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إنشاء النسخة الاحتياطية'})


@require_POST
@csrf_exempt
def ajax_recalculate_costs(request):
    """إعادة حساب التكلفة عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إعادة حساب التكلفة'})
