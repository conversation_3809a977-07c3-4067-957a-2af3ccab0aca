from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import logout, login
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import json
import os
import subprocess
import hashlib

from .models import (
    DeletedRecord, EditHistory, SystemBackup, SystemSettings,
    LicenseInfo, TaskbarSettings
)


# لوحة تحكم الخدمات
@login_required
def services_dashboard(request):
    """لوحة تحكم الخدمات"""

    # إحصائيات سريعة
    total_deleted_records = DeletedRecord.objects.filter(is_restored=False).count()
    total_edit_history = EditHistory.objects.count()
    total_backups = SystemBackup.objects.filter(is_valid=True).count()
    total_settings = SystemSettings.objects.count()

    # آخر العمليات
    recent_deleted = DeletedRecord.objects.filter(is_restored=False).order_by('-deleted_at')[:5]
    recent_edits = EditHistory.objects.order_by('-edited_at')[:5]
    recent_backups = SystemBackup.objects.filter(is_valid=True).order_by('-created_at')[:3]

    # معلومات الترخيص
    try:
        license_info = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_info = None

    # إعدادات شريط المهام للمستخدم الحالي
    try:
        taskbar_settings = TaskbarSettings.objects.get(user=request.user)
    except TaskbarSettings.DoesNotExist:
        taskbar_settings = None

    # إحصائيات النظام
    total_users = User.objects.filter(is_active=True).count()
    total_staff = User.objects.filter(is_staff=True, is_active=True).count()

    # حجم النسخ الاحتياطية
    total_backup_size = SystemBackup.objects.filter(is_valid=True).aggregate(
        total_size=models.Sum('file_size'))['total_size'] or 0

    # إعدادات النظام حسب الفئة
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = 0
        settings_by_category[setting.category] += 1

    context = {
        'title': 'لوحة تحكم الخدمات',
        'total_deleted_records': total_deleted_records,
        'total_edit_history': total_edit_history,
        'total_backups': total_backups,
        'total_settings': total_settings,
        'recent_deleted': recent_deleted,
        'recent_edits': recent_edits,
        'recent_backups': recent_backups,
        'license_info': license_info,
        'taskbar_settings': taskbar_settings,
        'total_users': total_users,
        'total_staff': total_staff,
        'total_backup_size': total_backup_size,
        'settings_by_category': settings_by_category,
    }
    return render(request, 'services/dashboard.html', context)


# إدارة البيانات
@login_required
@staff_member_required
def delete_data(request):
    """حذف البيانات المسجلة"""

    # قائمة النماذج المتاحة للحذف
    available_models = [
        {'name': 'Person', 'app': 'definitions', 'verbose_name': 'الأشخاص'},
        {'name': 'Item', 'app': 'definitions', 'verbose_name': 'الأصناف'},
        {'name': 'SalesInvoice', 'app': 'sales', 'verbose_name': 'فواتير المبيعات'},
        {'name': 'PurchaseInvoice', 'app': 'purchases', 'verbose_name': 'فواتير المشتريات'},
        {'name': 'JournalEntry', 'app': 'accounting', 'verbose_name': 'القيود المحاسبية'},
    ]

    if request.method == 'POST':
        selected_models = request.POST.getlist('models')
        date_from = request.POST.get('date_from')
        date_to = request.POST.get('date_to')
        confirm_delete = request.POST.get('confirm_delete')

        if not confirm_delete:
            messages.error(request, 'يجب تأكيد عملية الحذف')
            return redirect('services:delete_data')

        # تنفيذ عملية الحذف
        deleted_count = 0
        for model_name in selected_models:
            try:
                # هنا يمكن إضافة منطق الحذف الفعلي
                deleted_count += 1
            except Exception as e:
                messages.error(request, f'خطأ في حذف {model_name}: {str(e)}')

        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} نوع من البيانات بنجاح')

        return redirect('services:delete_data')

    context = {
        'title': 'حذف البيانات المسجلة',
        'available_models': available_models,
    }
    return render(request, 'services/delete_data.html', context)


@login_required
def recycle_bin(request):
    """سلة المحذوفات"""
    context = {
        'title': 'سلة المحذوفات',
    }
    return render(request, 'services/recycle_bin.html', context)


@login_required
def edit_history(request):
    """سلة التعديلات"""
    context = {
        'title': 'سلة التعديلات',
    }
    return render(request, 'services/edit_history.html', context)


@login_required
@staff_member_required
def backup(request):
    """النسخ الاحتياطي"""
    context = {
        'title': 'النسخ الاحتياطي',
    }
    return render(request, 'services/backup.html', context)


@login_required
def license_info(request):
    """معلومات الترخيص"""
    context = {
        'title': 'ترخيص النسخة',
    }
    return render(request, 'services/license.html', context)


@login_required
@staff_member_required
def recalculate_costs(request):
    """إعادة حساب أسعار تكلفة الأصناف"""
    context = {
        'title': 'إعادة حساب أسعار تكلفة الأصناف',
    }
    return render(request, 'services/recalculate_costs.html', context)


@login_required
def taskbar_settings(request):
    """إعدادات شريط المهام"""
    context = {
        'title': 'إعدادات شريط المهام',
    }
    return render(request, 'services/taskbar_settings.html', context)


@login_required
@staff_member_required
def system_settings(request):
    """كلمات السر وخيارات البرنامج"""
    context = {
        'title': 'كلمات السر وخيارات البرنامج',
    }
    return render(request, 'services/system_settings.html', context)


@login_required
def print_design(request):
    """تصميم نماذج الطباعة"""
    context = {
        'title': 'تصميم نماذج الطباعة',
    }
    return render(request, 'services/print_design.html', context)


@login_required
def barcode_design(request):
    """تصميم نماذج الباركود"""
    context = {
        'title': 'تصميم نماذج الباركود',
    }
    return render(request, 'services/barcode_design.html', context)


@login_required
def relogin(request):
    """إعادة الدخول كمستخدم آخر"""
    context = {
        'title': 'إعادة الدخول كمستخدم',
    }
    return render(request, 'services/relogin.html', context)


# AJAX endpoints
@require_POST
@csrf_exempt
def ajax_delete_records(request):
    """حذف السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الحذف بنجاح'})


@require_POST
@csrf_exempt
def ajax_restore_records(request):
    """استرداد السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الاسترداد بنجاح'})


@require_POST
@csrf_exempt
def ajax_create_backup(request):
    """إنشاء نسخة احتياطية عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إنشاء النسخة الاحتياطية'})


@require_POST
@csrf_exempt
def ajax_recalculate_costs(request):
    """إعادة حساب التكلفة عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إعادة حساب التكلفة'})
