{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-bell text-dark me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل لجميع العمليات والإشعارات المتعلقة بالفروع</p>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">جميع الفروع</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}" {% if branch.id|stringformat:"s" == branch_filter %}selected{% endif %}>
                                    {{ branch.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="operation_type" class="form-label">نوع العملية</label>
                            <select class="form-select" id="operation_type" name="operation_type">
                                <option value="">جميع العمليات</option>
                                <option value="cash" {% if operation_type == 'cash' %}selected{% endif %}>نقدية</option>
                                <option value="bank" {% if operation_type == 'bank' %}selected{% endif %}>بنكية</option>
                                <option value="goods" {% if operation_type == 'goods' %}selected{% endif %}>بضائع</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_notifications|default:0 }}</h4>
                            <p class="mb-0">إجمالي الإشعارات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ cash_count|default:0 }}</h4>
                            <p class="mb-0">عمليات نقدية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ bank_count|default:0 }}</h4>
                            <p class="mb-0">عمليات بنكية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-university fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ goods_count|default:0 }}</h4>
                            <p class="mb-0">تحويلات بضائع</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الإشعارات والعمليات
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>الفرع</th>
                                    <th>رقم العملية</th>
                                    <th>المبلغ/القيمة</th>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notification in page_obj %}
                                <tr>
                                    <td>
                                        {% if notification.type == 'نقدية' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-money-bill-wave me-1"></i>
                                                {{ notification.type }}
                                            </span>
                                        {% elif notification.type == 'بنكية' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-university me-1"></i>
                                                {{ notification.type }}
                                            </span>
                                        {% elif notification.type == 'بضائع' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-truck me-1"></i>
                                                {{ notification.type }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if notification.operation == 'واردة' %}
                                            <span class="badge bg-success">{{ notification.operation }}</span>
                                        {% elif notification.operation == 'صادرة' %}
                                            <span class="badge bg-danger">{{ notification.operation }}</span>
                                        {% elif notification.operation == 'إيداع' %}
                                            <span class="badge bg-primary">{{ notification.operation }}</span>
                                        {% elif notification.operation == 'سحب' %}
                                            <span class="badge bg-warning">{{ notification.operation }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ notification.operation }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ notification.branch }}</strong>
                                    </td>
                                    <td>
                                        <code>{{ notification.number }}</code>
                                    </td>
                                    <td class="text-end">
                                        <strong>{{ notification.amount|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {{ notification.date|date:"Y-m-d" }}
                                        <br>
                                        <small class="text-muted">{{ notification.date|date:"H:i" }}</small>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ notification.description|default:"-" }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if operation_type %}&operation_type={{ operation_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">السابق</a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if operation_type %}&operation_type={{ operation_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if operation_type %}&operation_type={{ operation_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إشعارات</h5>
                        <p class="text-muted">لم يتم العثور على أي إشعارات أو عمليات للفترة المحددة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .badge {
        color: #000 !important;
        background-color: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
    }
}
</style>
{% endblock %}
