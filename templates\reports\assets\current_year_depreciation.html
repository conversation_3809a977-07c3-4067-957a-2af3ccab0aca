{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-line text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:assets_reports' %}">تقارير الأصول الثابتة</a></li>
                            <li class="breadcrumb-item active">إهلاك السنة الحالية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">السنة</label>
                    <select name="year" class="form-select">
                        {% for year_option in years %}
                        <option value="{{ year_option }}" {% if year == year_option|stringformat:"s" %}selected{% endif %}>
                            {{ year_option }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">فئة الأصل</label>
                    <select name="category_id" class="form-select">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if depreciation_data %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_assets }}</h4>
                            <p class="mb-0">عدد الأصول</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_original_cost|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي التكلفة الأصلية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_current_year_depreciation|floatformat:2 }}</h4>
                            <p class="mb-0">إهلاك {{ year }}</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_accumulated_depreciation|floatformat:2 }}</h4>
                            <p class="mb-0">الإهلاك المتراكم</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-area"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Depreciation Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل إهلاك السنة الحالية {{ year }}
                <span class="badge bg-warning">{{ total_assets }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>كود الأصل</th>
                            <th>اسم الأصل</th>
                            <th>الفئة</th>
                            <th>تاريخ الشراء</th>
                            <th>التكلفة الأصلية</th>
                            <th>معدل الإهلاك</th>
                            <th>إهلاك {{ year }}</th>
                            <th>الإهلاك المتراكم</th>
                            <th>القيمة الدفترية</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for asset in depreciation_data %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ asset.code }}</strong>
                            </td>
                            <td>
                                <strong>{{ asset.name }}</strong>
                                {% if asset.description %}
                                <small class="text-muted d-block">{{ asset.description|truncatechars:50 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if asset.category %}
                                <span class="badge bg-secondary">{{ asset.category.name }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ asset.purchase_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong class="text-success">{{ asset.original_cost|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ asset.depreciation_rate|floatformat:1 }}%</span>
                            </td>
                            <td>
                                <strong class="text-warning">{{ asset.current_year_depreciation|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-danger">{{ asset.accumulated_depreciation|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-primary">{{ asset.book_value|floatformat:2 }}</strong>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4">الإجمالي</th>
                            <th class="text-success">{{ total_original_cost|floatformat:2 }}</th>
                            <th></th>
                            <th class="text-warning">{{ total_current_year_depreciation|floatformat:2 }}</th>
                            <th class="text-danger">{{ total_accumulated_depreciation|floatformat:2 }}</th>
                            <th class="text-primary">{{ total_book_value|floatformat:2 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- Monthly Breakdown -->
    {% if monthly_depreciation %}
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar me-2"></i>
                التوزيع الشهري للإهلاك {{ year }}
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-sm mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الشهر</th>
                            <th>مبلغ الإهلاك</th>
                            <th>النسبة من السنة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month in monthly_depreciation %}
                        <tr>
                            <td><strong>{{ month.month_name }}</strong></td>
                            <td class="text-warning">{{ month.amount|floatformat:2 }}</td>
                            <td>
                                {% if total_current_year_depreciation > 0 %}
                                {% widthratio month.amount total_current_year_depreciation 100 %}%
                                {% else %}
                                0%
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات إهلاك</h5>
            <p class="text-muted">لا توجد أصول ثابتة للسنة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
