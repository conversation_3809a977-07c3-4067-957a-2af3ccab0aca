{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-0">
                <i class="fas fa-window-maximize text-primary me-2"></i>
                {{ title }}
            </h2>
            <p class="text-muted mb-0">تخصيص إعدادات شريط المهام وترتيب القوائم</p>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات شريط المهام
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        
                        <!-- موضع الشريط -->
                        <div class="mb-4">
                            <label class="form-label">موضع شريط المهام:</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="position" value="NONE" id="pos_none"
                                               {% if settings.position == 'NONE' %}checked{% endif %}>
                                        <label class="form-check-label" for="pos_none">
                                            <i class="fas fa-ban me-2"></i>بلا
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="position" value="AUTO_HIDE" id="pos_auto"
                                               {% if settings.position == 'AUTO_HIDE' %}checked{% endif %}>
                                        <label class="form-check-label" for="pos_auto">
                                            <i class="fas fa-eye-slash me-2"></i>إخفاء تلقائي
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="position" value="VERTICAL" id="pos_vertical"
                                               {% if settings.position == 'VERTICAL' %}checked{% endif %}>
                                        <label class="form-check-label" for="pos_vertical">
                                            <i class="fas fa-arrows-alt-v me-2"></i>رأسي
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="position" value="HORIZONTAL" id="pos_horizontal"
                                               {% if settings.position == 'HORIZONTAL' or not settings %}checked{% endif %}>
                                        <label class="form-check-label" for="pos_horizontal">
                                            <i class="fas fa-arrows-alt-h me-2"></i>أفقي
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات العرض -->
                        <div class="mb-4">
                            <label class="form-label">خيارات العرض:</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="show_icons" id="show_icons"
                                               {% if settings.show_icons or not settings %}checked{% endif %}>
                                        <label class="form-check-label" for="show_icons">
                                            <i class="fas fa-icons me-2"></i>إظهار الأيقونات
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="show_text" id="show_text"
                                               {% if settings.show_text or not settings %}checked{% endif %}>
                                        <label class="form-check-label" for="show_text">
                                            <i class="fas fa-font me-2"></i>إظهار النص
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="auto_hide" id="auto_hide"
                                               {% if settings.auto_hide %}checked{% endif %}>
                                        <label class="form-check-label" for="auto_hide">
                                            <i class="fas fa-eye-slash me-2"></i>إخفاء تلقائي
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المظهر والحجم -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="theme" class="form-label">المظهر:</label>
                                <select class="form-select" id="theme" name="theme">
                                    <option value="default" {% if settings.theme == 'default' or not settings %}selected{% endif %}>افتراضي</option>
                                    <option value="dark" {% if settings.theme == 'dark' %}selected{% endif %}>داكن</option>
                                    <option value="light" {% if settings.theme == 'light' %}selected{% endif %}>فاتح</option>
                                    <option value="blue" {% if settings.theme == 'blue' %}selected{% endif %}>أزرق</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="size" class="form-label">الحجم:</label>
                                <select class="form-select" id="size" name="size">
                                    <option value="small" {% if settings.size == 'small' %}selected{% endif %}>صغير</option>
                                    <option value="medium" {% if settings.size == 'medium' or not settings %}selected{% endif %}>متوسط</option>
                                    <option value="large" {% if settings.size == 'large' %}selected{% endif %}>كبير</option>
                                </select>
                            </div>
                        </div>

                        <!-- ترتيب القوائم -->
                        <div class="mb-4">
                            <label class="form-label">ترتيب القوائم:</label>
                            <div class="card">
                                <div class="card-body">
                                    <div id="menu-sortable" class="list-group">
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="dashboard">
                                            <span><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="definitions">
                                            <span><i class="fas fa-cogs me-2"></i>التعريفات</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="sales">
                                            <span><i class="fas fa-shopping-cart me-2"></i>المبيعات</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="purchases">
                                            <span><i class="fas fa-shopping-bag me-2"></i>المشتريات</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="inventory">
                                            <span><i class="fas fa-boxes me-2"></i>المخازن</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="accounting">
                                            <span><i class="fas fa-calculator me-2"></i>الحسابات العامة</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="branches">
                                            <span><i class="fas fa-sitemap me-2"></i>المركز الرئيسي والفروع</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="services">
                                            <span><i class="fas fa-cogs me-2"></i>الخدمات</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center" data-menu="reports">
                                            <span><i class="fas fa-chart-bar me-2"></i>التقارير</span>
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                    </div>
                                    <small class="text-muted mt-2 d-block">اسحب العناصر لإعادة ترتيبها</small>
                                </div>
                            </div>
                        </div>

                        <!-- حقل مخفي لترتيب القوائم -->
                        <input type="hidden" id="menu_order" name="menu_order" value="">

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:home' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة شريط المهام
                    </h5>
                </div>
                <div class="card-body">
                    <div id="taskbar-preview" class="border rounded p-3 bg-light">
                        <div class="d-flex justify-content-center align-items-center" style="height: 60px;">
                            <span class="text-muted">معاينة شريط المهام ستظهر هنا</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make menu items sortable
    const sortable = document.getElementById('menu-sortable');
    if (sortable) {
        // Simple drag and drop implementation
        let draggedElement = null;
        
        sortable.addEventListener('dragstart', function(e) {
            draggedElement = e.target;
            e.target.style.opacity = '0.5';
        });
        
        sortable.addEventListener('dragend', function(e) {
            e.target.style.opacity = '';
            draggedElement = null;
        });
        
        sortable.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        
        sortable.addEventListener('drop', function(e) {
            e.preventDefault();
            if (draggedElement && e.target.classList.contains('list-group-item')) {
                const rect = e.target.getBoundingClientRect();
                const midpoint = rect.top + rect.height / 2;

                if (e.clientY < midpoint) {
                    sortable.insertBefore(draggedElement, e.target);
                } else {
                    sortable.insertBefore(draggedElement, e.target.nextSibling);
                }

                // Update menu order after reordering
                updateMenuOrder();
            }
        });
        
        // Make items draggable
        const items = sortable.querySelectorAll('.list-group-item');
        items.forEach(item => {
            item.draggable = true;
        });
    }
    
    // Update menu order when items are reordered
    function updateMenuOrder() {
        const items = sortable.querySelectorAll('.list-group-item');
        const order = Array.from(items).map(item => item.getAttribute('data-menu'));
        document.getElementById('menu_order').value = JSON.stringify(order);
    }

    // Update preview when settings change
    function updatePreview() {
        const position = document.querySelector('input[name="position"]:checked').value;
        const showIcons = document.getElementById('show_icons').checked;
        const showText = document.getElementById('show_text').checked;
        const theme = document.getElementById('theme').value;
        const size = document.getElementById('size').value;

        const preview = document.getElementById('taskbar-preview');

        // Apply theme to preview
        let themeClass = '';
        let positionStyle = '';

        switch(theme) {
            case 'dark':
                themeClass = 'bg-dark text-white';
                break;
            case 'light':
                themeClass = 'bg-light text-dark';
                break;
            case 'blue':
                themeClass = 'bg-primary text-white';
                break;
            default:
                themeClass = 'bg-secondary text-white';
        }

        switch(position) {
            case 'VERTICAL':
                positionStyle = 'writing-mode: vertical-lr; height: 200px; width: 60px;';
                break;
            case 'NONE':
                positionStyle = 'display: none;';
                break;
            case 'AUTO_HIDE':
                positionStyle = 'opacity: 0.5;';
                break;
            default:
                positionStyle = 'height: 60px;';
        }

        preview.className = `border rounded p-3 ${themeClass}`;
        preview.style = positionStyle;

        if (position === 'NONE') {
            preview.innerHTML = '<div class="text-center text-muted">شريط المهام مخفي</div>';
        } else {
            preview.innerHTML = `
                <div class="d-flex ${position === 'VERTICAL' ? 'flex-column' : ''} justify-content-center align-items-center h-100">
                    ${showIcons ? '<i class="fas fa-home me-2"></i>' : ''}
                    ${showText ? '<span>لوحة التحكم</span>' : ''}
                    ${showIcons ? '<i class="fas fa-cogs ms-2"></i>' : ''}
                    ${showText ? '<span class="ms-2">الخدمات</span>' : ''}
                </div>
            `;
        }
    }
    
    // Add event listeners
    document.querySelectorAll('input[name="position"]').forEach(radio => {
        radio.addEventListener('change', updatePreview);
    });
    
    document.getElementById('show_icons').addEventListener('change', updatePreview);
    document.getElementById('show_text').addEventListener('change', updatePreview);
    document.getElementById('theme').addEventListener('change', updatePreview);
    document.getElementById('size').addEventListener('change', updatePreview);
    
    // Initial setup
    updateMenuOrder();
    updatePreview();

    // Load saved menu order if available
    {% if settings.menu_order %}
    const savedOrder = {{ settings.menu_order|safe }};
    if (savedOrder && savedOrder.length > 0) {
        // Reorder items based on saved order
        savedOrder.forEach(menuId => {
            const item = sortable.querySelector(`[data-menu="${menuId}"]`);
            if (item) {
                sortable.appendChild(item);
            }
        });
        updateMenuOrder();
    }
    {% endif %}
});
</script>
{% endblock %}
