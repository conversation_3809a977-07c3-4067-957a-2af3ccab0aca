# Generated by Django 5.2.2 on 2025-06-07 15:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LicenseInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الترخيص')),
                ('license_type', models.CharField(choices=[('TRIAL', 'تجريبي'), ('BASIC', 'أساسي'), ('PROFESSIONAL', 'احترافي'), ('ENTERPRISE', 'مؤسسي')], max_length=20, verbose_name='نوع الترخيص')),
                ('customer_name', models.CharField(max_length=200, verbose_name='اسم العميل')),
                ('customer_email', models.EmailField(max_length=254, verbose_name='بريد العميل')),
                ('company_name', models.CharField(blank=True, max_length=200, verbose_name='اسم الشركة')),
                ('issued_date', models.DateTimeField(verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateTimeField(verbose_name='تاريخ الانتهاء')),
                ('activation_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التفعيل')),
                ('max_users', models.IntegerField(default=1, verbose_name='الحد الأقصى للمستخدمين')),
                ('max_branches', models.IntegerField(default=1, verbose_name='الحد الأقصى للفروع')),
                ('max_transactions', models.IntegerField(blank=True, null=True, verbose_name='الحد الأقصى للمعاملات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_trial', models.BooleanField(default=False, verbose_name='تجريبي')),
                ('features', models.JSONField(default=dict, verbose_name='المميزات المتاحة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'ترخيص',
                'verbose_name_plural': 'التراخيص',
                'ordering': ['-issued_date'],
            },
        ),
        migrations.CreateModel(
            name='DeletedRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(verbose_name='معرف الكائن')),
                ('object_data', models.JSONField(verbose_name='بيانات الكائن')),
                ('object_repr', models.CharField(max_length=200, verbose_name='تمثيل الكائن')),
                ('deleted_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحذف')),
                ('deletion_reason', models.TextField(blank=True, verbose_name='سبب الحذف')),
                ('is_restored', models.BooleanField(default=False, verbose_name='تم الاسترداد')),
                ('restored_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاسترداد')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع البيانات')),
                ('deleted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='حُذف بواسطة')),
                ('restored_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='restored_records', to=settings.AUTH_USER_MODEL, verbose_name='استُرد بواسطة')),
            ],
            options={
                'verbose_name': 'سجل محذوف',
                'verbose_name_plural': 'السجلات المحذوفة',
                'ordering': ['-deleted_at'],
            },
        ),
        migrations.CreateModel(
            name='EditHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(verbose_name='معرف الكائن')),
                ('field_name', models.CharField(max_length=100, verbose_name='اسم الحقل')),
                ('old_value', models.TextField(blank=True, verbose_name='القيمة القديمة')),
                ('new_value', models.TextField(blank=True, verbose_name='القيمة الجديدة')),
                ('edited_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعديل')),
                ('edit_reason', models.TextField(blank=True, verbose_name='سبب التعديل')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع البيانات')),
                ('edited_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='عُدل بواسطة')),
            ],
            options={
                'verbose_name': 'تاريخ تعديل',
                'verbose_name_plural': 'تاريخ التعديلات',
                'ordering': ['-edited_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('backup_name', models.CharField(max_length=200, verbose_name='اسم النسخة')),
                ('backup_type', models.CharField(choices=[('FULL', 'نسخة كاملة'), ('PARTIAL', 'نسخة جزئية'), ('INCREMENTAL', 'نسخة تزايدية')], default='FULL', max_length=20, verbose_name='نوع النسخة')),
                ('file_path', models.CharField(max_length=500, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(verbose_name='حجم الملف (بايت)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('include_media', models.BooleanField(default=True, verbose_name='تضمين ملفات الوسائط')),
                ('include_logs', models.BooleanField(default=False, verbose_name='تضمين ملفات السجلات')),
                ('compress_backup', models.BooleanField(default=True, verbose_name='ضغط النسخة')),
                ('is_valid', models.BooleanField(default=True, verbose_name='نسخة صالحة')),
                ('checksum', models.CharField(blank=True, max_length=64, verbose_name='المجموع التحققي')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
            ],
            options={
                'verbose_name': 'نسخة احتياطية',
                'verbose_name_plural': 'النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الإعداد')),
                ('value', models.TextField(verbose_name='قيمة الإعداد')),
                ('value_type', models.CharField(choices=[('STRING', 'نص'), ('INTEGER', 'رقم صحيح'), ('FLOAT', 'رقم عشري'), ('BOOLEAN', 'صحيح/خطأ'), ('JSON', 'JSON')], default='STRING', max_length=20, verbose_name='نوع القيمة')),
                ('category', models.CharField(max_length=50, verbose_name='الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_system', models.BooleanField(default=False, verbose_name='إعداد نظام')),
                ('is_editable', models.BooleanField(default=True, verbose_name='قابل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إعداد نظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['category', 'key'],
            },
        ),
        migrations.CreateModel(
            name='TaskbarSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.CharField(choices=[('NONE', 'بلا'), ('AUTO_HIDE', 'إخفاء تلقائي'), ('VERTICAL', 'رأسي'), ('HORIZONTAL', 'أفقي')], default='HORIZONTAL', max_length=20, verbose_name='موضع الشريط')),
                ('auto_hide', models.BooleanField(default=False, verbose_name='إخفاء تلقائي')),
                ('show_icons', models.BooleanField(default=True, verbose_name='إظهار الأيقونات')),
                ('show_text', models.BooleanField(default=True, verbose_name='إظهار النص')),
                ('menu_order', models.JSONField(default=list, verbose_name='ترتيب القوائم')),
                ('pinned_items', models.JSONField(default=list, verbose_name='العناصر المثبتة')),
                ('theme', models.CharField(default='default', max_length=20, verbose_name='المظهر')),
                ('size', models.CharField(default='medium', max_length=20, verbose_name='الحجم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إعدادات شريط المهام',
                'verbose_name_plural': 'إعدادات شريط المهام',
            },
        ),
    ]
