{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice text-danger me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'assets:depreciation_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة لحساب الإهلاك
                    </a>
                    <a href="{% url 'assets:calculate_depreciation' %}" class="btn btn-danger me-2">
                        <i class="fas fa-calculator me-1"></i>
                        حساب إهلاك شهري
                    </a>
                    <a href="{% url 'assets:depreciation_entry_create' %}" class="btn btn-outline-danger">
                        <i class="fas fa-plus me-1"></i>
                        إضافة قيد يدوي
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="{{ search_query }}"
                                       placeholder="البحث في قيود الإهلاك (رقم القيد، اسم الأصل)">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="year">
                                <option value="">جميع السنوات</option>
                                <option value="2020" {% if year_filter == "2020" %}selected{% endif %}>2020</option>
                                <option value="2021" {% if year_filter == "2021" %}selected{% endif %}>2021</option>
                                <option value="2022" {% if year_filter == "2022" %}selected{% endif %}>2022</option>
                                <option value="2023" {% if year_filter == "2023" %}selected{% endif %}>2023</option>
                                <option value="2024" {% if year_filter == "2024" %}selected{% endif %}>2024</option>
                                <option value="2025" {% if year_filter == "2025" %}selected{% endif %}>2025</option>
                                <option value="2026" {% if year_filter == "2026" %}selected{% endif %}>2026</option>
                                <option value="2027" {% if year_filter == "2027" %}selected{% endif %}>2027</option>
                                <option value="2028" {% if year_filter == "2028" %}selected{% endif %}>2028</option>
                                <option value="2029" {% if year_filter == "2029" %}selected{% endif %}>2029</option>
                                <option value="2030" {% if year_filter == "2030" %}selected{% endif %}>2030</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="month">
                                <option value="">جميع الشهور</option>
                                <option value="1" {% if month_filter == "1" %}selected{% endif %}>يناير</option>
                                <option value="2" {% if month_filter == "2" %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if month_filter == "3" %}selected{% endif %}>مارس</option>
                                <option value="4" {% if month_filter == "4" %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if month_filter == "5" %}selected{% endif %}>مايو</option>
                                <option value="6" {% if month_filter == "6" %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if month_filter == "7" %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if month_filter == "8" %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if month_filter == "9" %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if month_filter == "10" %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if month_filter == "11" %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if month_filter == "12" %}selected{% endif %}>ديسمبر</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{% url 'assets:depreciation_entry_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة قيود الإهلاك -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة قيود الإهلاك
                        {% if page_obj %}
                            <span class="badge bg-danger ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>تاريخ القيد</th>
                                        <th>الفترة</th>
                                        <th>الأصل</th>
                                        <th>مبلغ الإهلاك</th>
                                        <th>مجمع الإهلاك قبل</th>
                                        <th>مجمع الإهلاك بعد</th>
                                        <th>القيمة الدفترية بعد</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in page_obj %}
                                        <tr>
                                            <td><strong>{{ entry.entry_number }}</strong></td>
                                            <td>{{ entry.entry_date|date:"d/m/Y" }}</td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    {{ entry.period_month }}/{{ entry.period_year }}
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ entry.asset.name }}</strong>
                                                    <br><small class="text-muted">{{ entry.asset.asset_code }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-danger fw-bold">
                                                    {{ entry.depreciation_amount|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-secondary">
                                                    {{ entry.accumulated_depreciation_before|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-warning fw-bold">
                                                    {{ entry.accumulated_depreciation_after|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">
                                                    {{ entry.book_value_after|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'assets:depreciation_entry_detail' entry.pk %}" 
                                                       class="btn btn-outline-info" 
                                                       title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger delete-btn" 
                                                            data-id="{{ entry.pk }}"
                                                            data-name="{{ entry.entry_number }}"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-secondary print-btn" 
                                                            data-id="{{ entry.pk }}"
                                                            title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- إحصائيات سريعة -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h6>إجمالي قيود الإهلاك</h6>
                                        <h4>{{ page_obj.paginator.count }}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h6>إجمالي مبلغ الإهلاك</h6>
                                        <h4>
                                            {% for entry in page_obj %}
                                                {% if forloop.first %}
                                                    {% widthratio entry.depreciation_amount 1 1 as total_depreciation %}
                                                {% else %}
                                                    {% widthratio total_depreciation|add:entry.depreciation_amount 1 1 as total_depreciation %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ total_depreciation|default:0|floatformat:0 }} ر.س
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h6>متوسط الإهلاك الشهري</h6>
                                        <h4>
                                            {% if page_obj.paginator.count > 0 %}
                                                {% widthratio total_depreciation page_obj.paginator.count 1 as avg_depreciation %}
                                                {{ avg_depreciation|floatformat:0 }} ر.س
                                            {% else %}
                                                0 ر.س
                                            {% endif %}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h6>عدد الأصول المهلكة</h6>
                                        <h4>
                                            {% regroup page_obj by asset as asset_groups %}
                                            {{ asset_groups|length }}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1&search={{ search_query }}&year={{ year_filter }}&month={{ month_filter }}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&year={{ year_filter }}&month={{ month_filter }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&year={{ year_filter }}&month={{ month_filter }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&search={{ search_query }}&year={{ year_filter }}&month={{ month_filter }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد قيود إهلاك</h5>
                            <p class="text-muted">لم يتم العثور على أي قيود إهلاك مطابقة لمعايير البحث</p>
                            <div class="mt-3">
                                <a href="{% url 'assets:calculate_depreciation' %}" class="btn btn-danger me-2">
                                    <i class="fas fa-calculator me-2"></i>
                                    حساب إهلاك شهري
                                </a>
                                <a href="{% url 'assets:depreciation_entry_create' %}" class="btn btn-outline-danger">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة قيد يدوي
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات حول قيود الإهلاك
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-file-invoice me-2"></i>
                                قيود الإهلاك
                            </h6>
                            <ul class="mb-0">
                                <li>تسجل تلقائياً عند حساب الإهلاك الشهري</li>
                                <li>تحدث مجمع الإهلاك للأصل</li>
                                <li>تؤثر على القيمة الدفترية</li>
                                <li>لا يمكن تكرار القيد لنفس الأصل والفترة</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيهات مهمة
                            </h6>
                            <ul class="mb-0">
                                <li>حذف القيد يعكس تأثيره على الأصل</li>
                                <li>تأكد من صحة الفترة المحاسبية</li>
                                <li>راجع مبالغ الإهلاك قبل التأكيد</li>
                                <li>احتفظ بنسخ احتياطية من القيود</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف قيد إهلاك
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entryId = this.getAttribute('data-id');
            const entryNumber = this.getAttribute('data-name');
            
            if (confirm(`هل أنت متأكد من حذف قيد الإهلاك "${entryNumber}"؟\n\nتحذير: سيتم عكس تأثير القيد على الأصل.`)) {
                fetch(`/assets/depreciation/entries/${entryId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        });
    });

    // طباعة قيد إهلاك
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entryId = this.getAttribute('data-id');
            // يمكن إضافة رابط الطباعة هنا
            alert('سيتم إضافة وظيفة الطباعة قريباً');
        });
    });
});
</script>
{% endblock %}
