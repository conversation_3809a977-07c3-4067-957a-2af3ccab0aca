from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from .models import Branch, BranchOpeningBalance, GoodsTransfer, CashMovement, BankMovement, CollectionRevenue
from datetime import datetime, timedelta


@login_required
def branches_home(request):
    """لوحة تحكم الفروع"""
    # إحصائيات سريعة
    total_branches = Branch.objects.filter(is_active=True).count()
    active_branches = Branch.objects.filter(is_active=True).count()

    # إحصائيات الحركات الحديثة
    recent_cash_movements = CashMovement.objects.filter(is_active=True).order_by('-created_at')[:5]
    recent_bank_movements = BankMovement.objects.filter(is_active=True).order_by('-created_at')[:5]
    recent_goods_transfers = GoodsTransfer.objects.filter(is_active=True).order_by('-created_at')[:5]

    context = {
        'title': 'لوحة تحكم المركز الرئيسي والفروع',
        'total_branches': total_branches,
        'active_branches': active_branches,
        'recent_cash_movements': recent_cash_movements,
        'recent_bank_movements': recent_bank_movements,
        'recent_goods_transfers': recent_goods_transfers,
    }
    return render(request, 'branches/home.html', context)


@login_required
def branch_list(request):
    """قائمة الفروع"""
    search = request.GET.get('search', '')
    branches = Branch.objects.all()

    if search:
        branches = branches.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search) |
            Q(manager_name__icontains=search)
        )

    branches = branches.order_by('name')

    # Pagination
    paginator = Paginator(branches, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'title': 'تعريف الفروع',
        'page_obj': page_obj,
        'search': search,
    }
    return render(request, 'branches/branch_list.html', context)


@login_required
def branch_add(request):
    """إضافة فرع جديد"""
    if request.method == 'POST':
        # معالجة البيانات المرسلة
        name = request.POST.get('name')
        code = request.POST.get('code')
        address = request.POST.get('address')
        phone = request.POST.get('phone')
        email = request.POST.get('email')
        manager_name = request.POST.get('manager_name')

        # التحقق من عدم تكرار الكود
        if Branch.objects.filter(code=code).exists():
            messages.error(request, 'كود الفرع موجود مسبقاً')
            return render(request, 'branches/branch_form.html', {
                'title': 'إضافة فرع جديد',
                'form_data': request.POST
            })

        # إنشاء الفرع
        branch = Branch.objects.create(
            name=name,
            code=code,
            address=address,
            phone=phone,
            email=email,
            manager_name=manager_name,
            created_by=request.user
        )

        messages.success(request, 'تم إضافة الفرع بنجاح')
        return redirect('branches:branch_list')

    context = {
        'title': 'إضافة فرع جديد',
    }
    return render(request, 'branches/branch_form.html', context)


@login_required
def branch_edit(request, pk):
    """تعديل فرع"""
    branch = get_object_or_404(Branch, pk=pk)

    if request.method == 'POST':
        # معالجة البيانات المرسلة
        name = request.POST.get('name')
        code = request.POST.get('code')
        address = request.POST.get('address')
        phone = request.POST.get('phone')
        email = request.POST.get('email')
        manager_name = request.POST.get('manager_name')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if Branch.objects.filter(code=code).exclude(pk=pk).exists():
            messages.error(request, 'كود الفرع موجود مسبقاً')
            return render(request, 'branches/branch_form.html', {
                'title': 'تعديل الفرع',
                'branch': branch,
                'form_data': request.POST
            })

        # تحديث الفرع
        branch.name = name
        branch.code = code
        branch.address = address
        branch.phone = phone
        branch.email = email
        branch.manager_name = manager_name
        branch.is_active = is_active
        branch.save()

        messages.success(request, 'تم تحديث الفرع بنجاح')
        return redirect('branches:branch_list')

    context = {
        'title': 'تعديل الفرع',
        'branch': branch,
    }
    return render(request, 'branches/branch_form.html', context)


@login_required
def branch_delete(request, pk):
    """حذف فرع"""
    branch = get_object_or_404(Branch, pk=pk)

    if request.method == 'POST':
        branch.delete()
        messages.success(request, 'تم حذف الفرع بنجاح')
        return redirect('branches:branch_list')

    context = {
        'title': 'حذف الفرع',
        'branch': branch,
    }
    return render(request, 'branches/branch_confirm_delete.html', context)


# القيد الافتتاحي
@login_required
def opening_balance(request):
    """القيد الافتتاحي رصيد أول المدة للفروع"""
    context = {
        'title': 'القيد الافتتاحي رصيد أول المدة للفروع',
    }
    return render(request, 'branches/opening_balance.html', context)


@login_required
def opening_balance_add(request):
    """إضافة قيد افتتاحي"""
    context = {
        'title': 'إضافة قيد افتتاحي',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/opening_balance_form.html', context)


@login_required
def opening_balance_edit(request, pk):
    """تعديل قيد افتتاحي"""
    context = {
        'title': 'تعديل قيد افتتاحي',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/opening_balance_form.html', context)


# بضاعة مرحلة للفروع
@login_required
def goods_transfer(request):
    """بضاعة مرحلة للفروع"""
    context = {
        'title': 'بضاعة مرحلة للفروع',
    }
    return render(request, 'branches/goods_transfer.html', context)


@login_required
def goods_transfer_add(request):
    """إضافة تحويل بضاعة"""
    context = {
        'title': 'إضافة تحويل بضاعة',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/goods_transfer_form.html', context)


@login_required
def goods_transfer_edit(request, pk):
    """تعديل تحويل بضاعة"""
    context = {
        'title': 'تعديل تحويل بضاعة',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/goods_transfer_form.html', context)


# النقدية - الخزائن
@login_required
def cash_received(request):
    """نقدية واردة للخزينة من خزائن الفروع"""
    context = {
        'title': 'نقدية واردة للخزينة من خزائن الفروع',
    }
    return render(request, 'branches/cash_received.html', context)


@login_required
def cash_received_add(request):
    """إضافة نقدية واردة"""
    context = {
        'title': 'إضافة نقدية واردة',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/cash_movement_form.html', context)


@login_required
def cash_sent(request):
    """نقدية صادرة من الخزينة إلى خزائن الفروع"""
    context = {
        'title': 'نقدية صادرة من الخزينة إلى خزائن الفروع',
    }
    return render(request, 'branches/cash_sent.html', context)


@login_required
def cash_sent_add(request):
    """إضافة نقدية صادرة"""
    context = {
        'title': 'إضافة نقدية صادرة',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/cash_movement_form.html', context)


# البنوك - خزائن الفروع
@login_required
def bank_deposits_from_branches(request):
    """إيداعات بنكية واردة من خزائن الفروع"""
    context = {
        'title': 'إيداعات بنكية واردة من خزائن الفروع',
    }
    return render(request, 'branches/bank_deposits_from_branches.html', context)


@login_required
def bank_deposits_from_branches_add(request):
    """إضافة إيداع بنكي من خزينة فرع"""
    context = {
        'title': 'إضافة إيداع بنكي من خزينة فرع',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/bank_movement_form.html', context)


@login_required
def bank_withdrawals_to_branches(request):
    """مسحوبات بنكية تم إيداعها في خزائن الفروع"""
    context = {
        'title': 'مسحوبات بنكية تم إيداعها في خزائن الفروع',
    }
    return render(request, 'branches/bank_withdrawals_to_branches.html', context)


@login_required
def bank_withdrawals_to_branches_add(request):
    """إضافة مسحوب بنكي لخزينة فرع"""
    context = {
        'title': 'إضافة مسحوب بنكي لخزينة فرع',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/bank_movement_form.html', context)


# البنوك - بنوك الفروع
@login_required
def bank_deposits_from_branch_banks(request):
    """إيداعات بنكية واردة من بنوك الفروع"""
    context = {
        'title': 'إيداعات بنكية واردة من بنوك الفروع',
    }
    return render(request, 'branches/bank_deposits_from_branch_banks.html', context)


@login_required
def bank_deposits_from_branch_banks_add(request):
    """إضافة إيداع بنكي من بنك فرع"""
    context = {
        'title': 'إضافة إيداع بنكي من بنك فرع',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/bank_movement_form.html', context)


@login_required
def bank_withdrawals_to_branch_banks(request):
    """مسحوبات بنكية تم إيداعها في بنوك الفروع"""
    context = {
        'title': 'مسحوبات بنكية تم إيداعها في بنوك الفروع',
    }
    return render(request, 'branches/bank_withdrawals_to_branch_banks.html', context)


@login_required
def bank_withdrawals_to_branch_banks_add(request):
    """إضافة مسحوب بنكي لبنك فرع"""
    context = {
        'title': 'إضافة مسحوب بنكي لبنك فرع',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/bank_movement_form.html', context)


# الإيرادات التحصيلية
@login_required
def collection_revenues(request):
    """إيرادات تحصيلية تخص الفروع"""
    context = {
        'title': 'إيرادات تحصيلية تخص الفروع',
    }
    return render(request, 'branches/collection_revenues.html', context)


@login_required
def collection_revenues_add(request):
    """إضافة إيراد تحصيلي"""
    context = {
        'title': 'إضافة إيراد تحصيلي',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/collection_revenue_form.html', context)


@login_required
def collection_revenues_edit(request, pk):
    """تعديل إيراد تحصيلي"""
    context = {
        'title': 'تعديل إيراد تحصيلي',
        'branches': Branch.objects.filter(is_active=True),
    }
    return render(request, 'branches/collection_revenue_form.html', context)
