{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-money-bill-wave text-danger me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'treasury:home' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للخزينة
                    </a>
                    <a href="{% url 'treasury:payment_create' %}" class="btn btn-danger">
                        <i class="fas fa-plus me-1"></i>
                        إضافة إيصال دفع
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="{{ search_query }}"
                                       placeholder="البحث في إيصالات الدفع (رقم الإيصال، المورد، البيان)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="{% url 'treasury:payment_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة إيصالات الدفع -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة إيصالات الدفع
                        {% if page_obj %}
                            <span class="badge bg-danger ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الإيصال</th>
                                        <th>التاريخ</th>
                                        <th>الخزينة</th>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>البيان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in page_obj %}
                                        <tr>
                                            <td><strong>{{ payment.payment_number }}</strong></td>
                                            <td>{{ payment.date|date:"d/m/Y" }}</td>
                                            <td>{{ payment.treasury.name }}</td>
                                            <td>{{ payment.supplier.name }}</td>
                                            <td>
                                                <span class="text-danger fw-bold">
                                                    {{ payment.amount|floatformat:2 }} ر.س
                                                </span>
                                            </td>
                                            <td>
                                                {% if payment.payment_method == 'CASH' %}
                                                    <span class="badge bg-success">{{ payment.get_payment_method_display }}</span>
                                                {% elif payment.payment_method == 'CHECK' %}
                                                    <span class="badge bg-warning">{{ payment.get_payment_method_display }}</span>
                                                {% elif payment.payment_method == 'BANK_TRANSFER' %}
                                                    <span class="badge bg-info">{{ payment.get_payment_method_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ payment.description|truncatechars:40 }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'treasury:payment_edit' payment.pk %}" 
                                                       class="btn btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger delete-btn" 
                                                            data-id="{{ payment.pk }}"
                                                            data-name="{{ payment.payment_number }}"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-info print-btn" 
                                                            data-id="{{ payment.pk }}"
                                                            title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إيصالات دفع</h5>
                            <p class="text-muted">لم يتم العثور على أي إيصالات دفع مطابقة لمعايير البحث</p>
                            <a href="{% url 'treasury:payment_create' %}" class="btn btn-danger">
                                <i class="fas fa-plus me-2"></i>
                                إضافة إيصال دفع جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف إيصال دفع
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.getAttribute('data-id');
            const paymentNumber = this.getAttribute('data-name');
            
            if (confirm(`هل أنت متأكد من حذف إيصال الدفع "${paymentNumber}"؟`)) {
                fetch(`/treasury/payments/${paymentId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        });
    });

    // طباعة إيصال
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.getAttribute('data-id');
            // يمكن إضافة رابط الطباعة هنا
            alert('سيتم إضافة وظيفة الطباعة قريباً');
        });
    });
});
</script>
{% endblock %}
