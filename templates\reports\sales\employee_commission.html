{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-friends text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:sales_reports' %}">تقارير المبيعات</a></li>
                            <li class="breadcrumb-item active">عمولة الموظف</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">الموظف</label>
                    <select name="employee_id" class="form-select">
                        <option value="">جميع الموظفين</option>
                        {% for emp in employees %}
                        <option value="{{ emp.id }}" {% if selected_employee == emp.id|stringformat:"s" %}selected{% endif %}>
                            {{ emp.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if commissions %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_sales|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_commission|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي العمولات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_employees }}</h4>
                            <p class="mb-0">عدد الموظفين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-user-friends"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Commission Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل عمولات الموظفين
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>إجمالي المبيعات</th>
                            <th>عدد الفواتير</th>
                            <th>نسبة العمولة</th>
                            <th>مبلغ العمولة</th>
                            <th>العمولة المدفوعة</th>
                            <th>العمولة المستحقة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for commission in commissions %}
                        <tr>
                            <td>
                                <strong>{{ commission.employee_name }}</strong>
                                {% if commission.employee_code %}
                                <small class="text-muted d-block">كود: {{ commission.employee_code }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if commission.department %}
                                <span class="badge bg-secondary">{{ commission.department }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-success">{{ commission.total_sales|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ commission.invoice_count }}</span>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ commission.commission_rate|floatformat:1 }}%</span>
                            </td>
                            <td>
                                <strong class="text-primary">{{ commission.commission_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="text-success">{{ commission.paid_commission|floatformat:2 }}</span>
                            </td>
                            <td>
                                <strong class="text-warning">{{ commission.due_commission|floatformat:2 }}</strong>
                            </td>
                            <td>
                                {% if commission.status == 'PENDING' %}
                                <span class="badge bg-warning">معلقة</span>
                                {% elif commission.status == 'PAID' %}
                                <span class="badge bg-success">مدفوعة</span>
                                {% elif commission.status == 'PARTIAL' %}
                                <span class="badge bg-info">جزئية</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ commission.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th class="text-success">{{ total_sales|floatformat:2 }}</th>
                            <th></th>
                            <th></th>
                            <th class="text-primary">{{ total_commission|floatformat:2 }}</th>
                            <th class="text-success">{{ total_paid|floatformat:2 }}</th>
                            <th class="text-warning">{{ total_due|floatformat:2 }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عمولات موظفين</h5>
            <p class="text-muted">لا توجد عمولات موظفين في الفترة المحددة</p>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    هذا التقرير يتطلب وجود موظفين مربوطين بفواتير المبيعات مع تحديد نسب العمولة
                </small>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
