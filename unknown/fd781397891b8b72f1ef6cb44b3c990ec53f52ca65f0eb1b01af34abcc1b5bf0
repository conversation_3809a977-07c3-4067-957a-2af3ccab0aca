{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-bar text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:sales_reports' %}">تقارير المبيعات</a></li>
                            <li class="breadcrumb-item active">عروض الأسعار الإجمالي</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-5">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if stats %}
    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ stats.total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي قيمة العروض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ stats.count }}</h4>
                            <p class="mb-0">عدد العروض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-file-contract"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ stats.accepted_count }}</h4>
                            <p class="mb-0">العروض المقبولة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ stats.avg_amount|floatformat:2 }}</h4>
                            <p class="mb-0">متوسط قيمة العرض</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Breakdown -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع العروض حسب الحالة
                    </h5>
                </div>
                <div class="card-body">
                    {% if status_breakdown %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الحالة</th>
                                    <th>العدد</th>
                                    <th>القيمة</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for status in status_breakdown %}
                                <tr>
                                    <td>
                                        {% if status.status == 'DRAFT' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif status.status == 'SENT' %}
                                        <span class="badge bg-info">مرسل</span>
                                        {% elif status.status == 'ACCEPTED' %}
                                        <span class="badge bg-success">مقبول</span>
                                        {% elif status.status == 'REJECTED' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                        {% elif status.status == 'EXPIRED' %}
                                        <span class="badge bg-warning">منتهي الصلاحية</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ status.count }}</td>
                                    <td>{{ status.total_amount|floatformat:2 }}</td>
                                    <td>
                                        {% if stats.total_amount > 0 %}
                                        {% widthratio status.total_amount stats.total_amount 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-percentage me-2"></i>
                        معدل القبول
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="display-4 text-success mb-3">
                        {% if stats.count > 0 %}
                        {% widthratio stats.accepted_count stats.count 100 %}%
                        {% else %}
                        0%
                        {% endif %}
                    </div>
                    <p class="text-muted">
                        {{ stats.accepted_count }} من أصل {{ stats.count }} عرض
                    </p>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" 
                             role="progressbar" 
                             style="width: {% if stats.count > 0 %}{% widthratio stats.accepted_count stats.count 100 %}{% else %}0{% endif %}%">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers -->
    {% if customer_quotations %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>
                أفضل العملاء حسب قيمة العروض
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم العميل</th>
                            <th>عدد العروض</th>
                            <th>إجمالي القيمة</th>
                            <th>العروض المقبولة</th>
                            <th>معدل القبول</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customer_quotations %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ forloop.counter }}</span>
                            </td>
                            <td>
                                <strong>{{ customer.customer__name }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.count }}</span>
                            </td>
                            <td>
                                <strong class="text-warning">{{ customer.total_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ customer.accepted_count }}</span>
                            </td>
                            <td>
                                {% if customer.count > 0 %}
                                {% widthratio customer.accepted_count customer.count 100 %}%
                                {% else %}
                                0%
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات عروض أسعار</h5>
            <p class="text-muted">لا توجد عروض أسعار في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
