# Environment Variables for Osaric Accounts System
# متغيرات البيئة لنظام حسابات أوساريك

# Django Settings
DEBUG=False
SECRET_KEY=your-very-secret-key-here-change-this-in-production
DJANGO_SETTINGS_MODULE=osaric_accounts.settings_production

# Database Configuration
DB_ENGINE=django.db.backends.postgresql
DB_NAME=osaric_accounts
DB_USER=osaric_user
DB_PASSWORD=your-database-password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/1

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Security Settings
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost,127.0.0.1

# Static and Media Files
STATIC_URL=/static/
MEDIA_URL=/media/

# Admin URL (for security)
ADMIN_URL=secure-admin/

# Backup Settings
BACKUP_ROOT=/app/backups

# Monitoring (Sentry)
SENTRY_DSN=your-sentry-dsn-here

# SSL Settings (for production)
USE_SSL=True
SECURE_SSL_REDIRECT=True

# Session Settings
SESSION_COOKIE_AGE=86400
SESSION_EXPIRE_AT_BROWSER_CLOSE=True

# File Upload Settings
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880
DATA_UPLOAD_MAX_MEMORY_SIZE=5242880

# Logging Level
LOG_LEVEL=INFO

# Time Zone
TIME_ZONE=Africa/Cairo

# Language
LANGUAGE_CODE=ar

# Currency Settings
DEFAULT_CURRENCY=EGP
CURRENCY_DECIMAL_PLACES=2

# Company Information
COMPANY_NAME=شركة أوساريك للتجارة
COMPANY_ADDRESS=القاهرة، مصر
COMPANY_PHONE=+20123456789
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=www.osaric.com

# System Settings
SYSTEM_NAME=حسابات أوساريك
SYSTEM_VERSION=1.0.0
SYSTEM_DESCRIPTION=نظام إدارة الحسابات المالية والمخزون

# Backup Settings
AUTO_BACKUP_ENABLED=True
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Performance Settings
CACHE_TIMEOUT=3600
SESSION_CACHE_ALIAS=default

# API Settings
API_THROTTLE_RATE_ANON=100/hour
API_THROTTLE_RATE_USER=1000/hour

# Development Settings (only for development)
# DEBUG_TOOLBAR_ENABLED=False
# INTERNAL_IPS=127.0.0.1,localhost
