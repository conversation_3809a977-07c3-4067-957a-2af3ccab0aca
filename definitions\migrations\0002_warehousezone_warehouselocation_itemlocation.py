# Generated by Django 5.2.2 on 2025-06-06 11:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WarehouseZone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, verbose_name='كود المنطقة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنطقة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='مكيف الهواء')),
                ('humidity_controlled', models.BooleanField(default=False, verbose_name='مراقب الرطوبة')),
                ('security_level', models.CharField(choices=[('LOW', 'منخفض'), ('MEDIUM', 'متوسط'), ('HIGH', 'عالي'), ('CRITICAL', 'حرج')], default='MEDIUM', max_length=20, verbose_name='مستوى الأمان')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'منطقة مخزن',
                'verbose_name_plural': 'مناطق المخازن',
                'ordering': ['warehouse', 'name'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=30, verbose_name='كود الموقع')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموقع')),
                ('aisle', models.CharField(blank=True, max_length=10, verbose_name='الممر')),
                ('rack', models.CharField(blank=True, max_length=10, verbose_name='الرف')),
                ('shelf', models.CharField(blank=True, max_length=10, verbose_name='الطبقة')),
                ('bin', models.CharField(blank=True, max_length=10, verbose_name='الصندوق')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعة')),
                ('max_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الحد الأقصى للوزن (كجم)')),
                ('is_available', models.BooleanField(default=True, verbose_name='متاح')),
                ('is_pickable', models.BooleanField(default=True, verbose_name='قابل للانتقاء')),
                ('is_receivable', models.BooleanField(default=True, verbose_name='قابل للاستقبال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('capacity_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة السعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
                ('zone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousezone', verbose_name='المنطقة')),
            ],
            options={
                'verbose_name': 'موقع مخزن',
                'verbose_name_plural': 'مواقع المخازن',
                'ordering': ['warehouse', 'aisle', 'rack', 'shelf', 'bin'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='ItemLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('min_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى')),
                ('max_quantity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأقصى')),
                ('reorder_point', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='نقطة إعادة الطلب')),
                ('priority', models.IntegerField(default=1, verbose_name='الأولوية')),
                ('location_type', models.CharField(choices=[('PRIMARY', 'أساسي'), ('SECONDARY', 'ثانوي'), ('OVERFLOW', 'فائض'), ('PICKING', 'انتقاء'), ('RESERVE', 'احتياطي')], default='PRIMARY', max_length=20, verbose_name='نوع الموقع')),
                ('is_default', models.BooleanField(default=False, verbose_name='الموقع الافتراضي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_date', models.DateField(auto_now_add=True, verbose_name='تاريخ التخصيص')),
                ('last_movement_date', models.DateTimeField(blank=True, null=True, verbose_name='آخر حركة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouselocation', verbose_name='الموقع')),
            ],
            options={
                'verbose_name': 'موقع صنف',
                'verbose_name_plural': 'مواقع الأصناف',
                'ordering': ['item', 'warehouse', 'priority'],
                'unique_together': {('item', 'warehouse', 'location')},
            },
        ),
    ]
