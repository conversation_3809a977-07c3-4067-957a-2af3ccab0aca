{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:assets_reports' %}">تقارير الأصول الثابتة</a></li>
                            <li class="breadcrumb-item active">أرباح بيع الأصول</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if sales %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_sales }}</h4>
                            <p class="mb-0">عدد الأصول المباعة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_sale_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي مبلغ البيع</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_book_value|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي القيمة الدفترية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-book"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-{% if total_profit >= 0 %}success{% else %}danger{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_profit|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي الربح/الخسارة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-{% if total_profit >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل أرباح بيع الأصول الثابتة
                <span class="badge bg-primary">{{ total_sales }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>كود الأصل</th>
                            <th>اسم الأصل</th>
                            <th>تاريخ البيع</th>
                            <th>التكلفة الأصلية</th>
                            <th>الإهلاك المتراكم</th>
                            <th>القيمة الدفترية</th>
                            <th>مبلغ البيع</th>
                            <th>الربح/الخسارة</th>
                            <th>نسبة الربح</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ sale.asset_code }}</strong>
                            </td>
                            <td>
                                <strong>{{ sale.asset_name }}</strong>
                            </td>
                            <td>{{ sale.sale_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong class="text-info">{{ sale.original_cost|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-warning">{{ sale.accumulated_depreciation|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-secondary">{{ sale.book_value|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-primary">{{ sale.sale_amount|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="{% if sale.profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ sale.profit|floatformat:2 }}
                                </strong>
                            </td>
                            <td>
                                {% if sale.book_value > 0 %}
                                <span class="badge {% if sale.profit >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                    {% widthratio sale.profit sale.book_value 100 %}%
                                </span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5">الإجمالي</th>
                            <th class="text-secondary">{{ total_book_value|floatformat:2 }}</th>
                            <th class="text-primary">{{ total_sale_amount|floatformat:2 }}</th>
                            <th class="{% if total_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ total_profit|floatformat:2 }}
                            </th>
                            <th>
                                {% if total_book_value > 0 %}
                                <span class="badge {% if total_profit >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                    {% widthratio total_profit total_book_value 100 %}%
                                </span>
                                {% endif %}
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مبيعات أصول ثابتة</h5>
            <p class="text-muted">لا توجد مبيعات أصول ثابتة في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
