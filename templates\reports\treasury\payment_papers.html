{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-contract text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:treasury_reports' %}">تقارير الخزينة</a></li>
                            <li class="breadcrumb-item active">أوراق الدفع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">المورد</label>
                    <select name="supplier_id" class="form-select">
                        <option value="">جميع الموردين</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if selected_supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع الورقة</label>
                    <select name="paper_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="CHECK" {% if selected_paper_type == 'CHECK' %}selected{% endif %}>شيك</option>
                        <option value="PROMISSORY_NOTE" {% if selected_paper_type == 'PROMISSORY_NOTE' %}selected{% endif %}>سند إذني</option>
                        <option value="BILL_OF_EXCHANGE" {% if selected_paper_type == 'BILL_OF_EXCHANGE' %}selected{% endif %}>كمبيالة</option>
                        <option value="BANK_DRAFT" {% if selected_paper_type == 'BANK_DRAFT' %}selected{% endif %}>حوالة بنكية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="PENDING" {% if selected_status == 'PENDING' %}selected{% endif %}>معلق</option>
                        <option value="ISSUED" {% if selected_status == 'ISSUED' %}selected{% endif %}>صادر</option>
                        <option value="PAID" {% if selected_status == 'PAID' %}selected{% endif %}>مدفوع</option>
                        <option value="BOUNCED" {% if selected_status == 'BOUNCED' %}selected{% endif %}>مرتد</option>
                        <option value="CANCELLED" {% if selected_status == 'CANCELLED' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if payment_papers %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_papers }}</h4>
                            <p class="mb-0">إجمالي أوراق الدفع</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-file-contract"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المبلغ</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ pending_papers }}</h4>
                            <p class="mb-0">أوراق معلقة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ paid_papers }}</h4>
                            <p class="mb-0">أوراق مدفوعة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Papers -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل أوراق الدفع
                <span class="badge bg-warning">{{ total_papers }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الورقة</th>
                            <th>نوع الورقة</th>
                            <th>المورد</th>
                            <th>المبلغ</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>البنك</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for paper in payment_papers %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ paper.paper_number }}</strong>
                            </td>
                            <td>
                                {% if paper.paper_type == 'CHECK' %}
                                <span class="badge bg-info">شيك</span>
                                {% elif paper.paper_type == 'PROMISSORY_NOTE' %}
                                <span class="badge bg-warning">سند إذني</span>
                                {% elif paper.paper_type == 'BILL_OF_EXCHANGE' %}
                                <span class="badge bg-secondary">كمبيالة</span>
                                {% elif paper.paper_type == 'BANK_DRAFT' %}
                                <span class="badge bg-primary">حوالة بنكية</span>
                                {% else %}
                                <span class="badge bg-dark">{{ paper.paper_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ paper.supplier_name }}</strong>
                            </td>
                            <td>
                                <strong class="text-warning">{{ paper.amount|floatformat:2 }}</strong>
                            </td>
                            <td>{{ paper.issue_date|date:"Y-m-d" }}</td>
                            <td>
                                {{ paper.due_date|date:"Y-m-d" }}
                                {% now "Y-m-d" as today %}
                                {% if paper.due_date|date:"Y-m-d" < today and paper.status != 'PAID' %}
                                <small class="text-danger d-block">متأخر</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if paper.bank_name %}
                                <small class="text-muted">{{ paper.bank_name }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if paper.status == 'PENDING' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif paper.status == 'ISSUED' %}
                                <span class="badge bg-info">صادر</span>
                                {% elif paper.status == 'PAID' %}
                                <span class="badge bg-success">مدفوع</span>
                                {% elif paper.status == 'BOUNCED' %}
                                <span class="badge bg-danger">مرتد</span>
                                {% elif paper.status == 'CANCELLED' %}
                                <span class="badge bg-secondary">ملغي</span>
                                {% else %}
                                <span class="badge bg-dark">{{ paper.status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if paper.notes %}
                                <small class="text-muted">{{ paper.notes|truncatechars:30 }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th class="text-warning">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="5"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أوراق دفع</h5>
            <p class="text-muted">لا توجد أوراق دفع في الفترة المحددة</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
