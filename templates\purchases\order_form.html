{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-clipboard-list text-warning me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'purchases:order_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات أمر الشراء
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.order_number.id_for_label }}" class="form-label">{{ form.order_number.label }}</label>
                                {{ form.order_number }}
                                {% if form.order_number.errors %}
                                    <div class="text-danger small">{{ form.order_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">{{ form.supplier.label }}</label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger small">{{ form.supplier.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- تاريخ التسليم والملاحظات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ التسليم والملاحظات
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">{{ form.expected_delivery_date.label }}</label>
                                {{ form.expected_delivery_date }}
                                {% if form.expected_delivery_date.errors %}
                                    <div class="text-danger small">{{ form.expected_delivery_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'purchases:order_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملاحظة -->
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> بعد حفظ أمر الشراء، يمكنك إضافة الأصناف المطلوبة من صفحة التفاصيل.
            </div>
        </div>
    </div>
</div>

<script>
// تحديد تاريخ اليوم افتراضياً
document.addEventListener('DOMContentLoaded', function() {
    const dateField = document.getElementById('{{ form.date.id_for_label }}');
    if (!dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }
    
    // تحديد تاريخ التسليم المتوقع (أسبوع من اليوم)
    const deliveryDateField = document.getElementById('{{ form.expected_delivery_date.id_for_label }}');
    if (!deliveryDateField.value) {
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        deliveryDateField.value = nextWeek.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
