# Generated by Django 5.2.2 on 2025-06-06 22:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_add_printer'),
        ('inventory', '0007_warehousetransfer_warehousetransferitem'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemTransformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transformation_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transformation_reason', models.CharField(max_length=200, verbose_name='سبب التحويل')),
                ('transformation_type', models.CharField(choices=[('ASSEMBLY', 'تجميع'), ('DISASSEMBLY', 'تفكيك'), ('CONVERSION', 'تحويل'), ('REPACKAGING', 'إعادة تعبئة'), ('QUALITY_CHANGE', 'تغيير جودة')], max_length=20, verbose_name='نوع التحويل')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_input_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي قيمة المدخلات')),
                ('total_output_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي قيمة المخرجات')),
                ('transformation_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة التحويل')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transformations', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تحويل من صنف إلى صنف',
                'verbose_name_plural': 'تحويلات من صنف إلى صنف',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ItemTransformationInput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المستهلكة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف المستهلك')),
                ('transformation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inputs', to='inventory.itemtransformation', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مدخل تحويل صنف',
                'verbose_name_plural': 'مدخلات تحويل الأصناف',
            },
        ),
        migrations.CreateModel(
            name='ItemTransformationOutput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المنتجة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف المنتج')),
                ('transformation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outputs', to='inventory.itemtransformation', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مخرج تحويل صنف',
                'verbose_name_plural': 'مخرجات تحويل الأصناف',
            },
        ),
    ]
