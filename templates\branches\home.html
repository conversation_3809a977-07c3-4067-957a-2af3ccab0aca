{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-sitemap text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة الفروع والحركات بين المركز الرئيسي والفروع</p>
                </div>
                <div>
                    <a href="{% url 'branches:branch_add' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_branches }}</h4>
                            <p class="mb-0">إجمالي الفروع</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ active_branches }}</h4>
                            <p class="mb-0">الفروع النشطة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ recent_cash_movements|length }}</h4>
                            <p class="mb-0">حركات نقدية حديثة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ recent_bank_movements|length }}</h4>
                            <p class="mb-0">حركات بنكية حديثة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-university"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'branches:branch_list' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-building fa-2x mb-2"></i>
                                <span>تعريف الفروع</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'branches:opening_balance' %}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <span>القيد الافتتاحي</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'branches:goods_transfer' %}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-truck fa-2x mb-2"></i>
                                <span>بضاعة مرحلة</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'branches:cash_received' %}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <span>حركات النقدية</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        آخر الحركات النقدية
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_cash_movements %}
                    <div class="list-group list-group-flush">
                        {% for movement in recent_cash_movements %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ movement.movement_number }}</h6>
                                <p class="mb-1">{{ movement.branch.name }}</p>
                                <small class="text-muted">{{ movement.movement_date }}</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ movement.amount }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">لا توجد حركات نقدية حديثة</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-university me-2"></i>
                        آخر الحركات البنكية
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_bank_movements %}
                    <div class="list-group list-group-flush">
                        {% for movement in recent_bank_movements %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ movement.movement_number }}</h6>
                                <p class="mb-1">{{ movement.branch.name }}</p>
                                <small class="text-muted">{{ movement.movement_date }}</small>
                            </div>
                            <span class="badge bg-info rounded-pill">{{ movement.amount }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">لا توجد حركات بنكية حديثة</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
