{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-pie text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">التقارير</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'reports:assets_reports' %}">تقارير الأصول الثابتة</a></li>
                            <li class="breadcrumb-item active">التقرير الإجمالي</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">فئة الأصل</label>
                    <select name="category_id" class="form-select">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if summary_data %}
    <!-- Overall Summary -->
    <div class="row mb-4">
        <div class="col-lg-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ summary_data.total_assets }}</h4>
                            <p class="mb-0">إجمالي عدد الأصول</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ summary_data.total_original_cost|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي التكلفة الأصلية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ summary_data.total_accumulated_depreciation|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي الإهلاك المتراكم</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-area"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ summary_data.total_book_value|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي القيمة الدفترية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Breakdown -->
    {% if category_summary %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                توزيع الأصول حسب الفئة
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الفئة</th>
                            <th>عدد الأصول</th>
                            <th>التكلفة الأصلية</th>
                            <th>الإهلاك المتراكم</th>
                            <th>القيمة الدفترية</th>
                            <th>النسبة من الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in category_summary %}
                        <tr>
                            <td>
                                <strong>{{ category.category_name|default:"غير محدد" }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ category.count }}</span>
                            </td>
                            <td>
                                <strong class="text-success">{{ category.total_original_cost|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-warning">{{ category.total_accumulated_depreciation|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <strong class="text-info">{{ category.total_book_value|floatformat:2 }}</strong>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">
                                        {% if summary_data.total_original_cost > 0 %}
                                        {% widthratio category.total_original_cost summary_data.total_original_cost 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </span>
                                    <div class="progress flex-grow-1" style="height: 10px;">
                                        <div class="progress-bar bg-success" 
                                             role="progressbar" 
                                             style="width: {% if summary_data.total_original_cost > 0 %}{% widthratio category.total_original_cost summary_data.total_original_cost 100 %}{% else %}0{% endif %}%">
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>الإجمالي</th>
                            <th class="text-primary">{{ summary_data.total_assets }}</th>
                            <th class="text-success">{{ summary_data.total_original_cost|floatformat:2 }}</th>
                            <th class="text-warning">{{ summary_data.total_accumulated_depreciation|floatformat:2 }}</th>
                            <th class="text-info">{{ summary_data.total_book_value|floatformat:2 }}</th>
                            <th>100%</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Status Breakdown -->
    {% if status_summary %}
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الأصول حسب الحالة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الحالة</th>
                                    <th>العدد</th>
                                    <th>القيمة الدفترية</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for status in status_summary %}
                                <tr>
                                    <td>
                                        {% if status.status == 'ACTIVE' %}
                                        <span class="badge bg-success">نشط</span>
                                        {% elif status.status == 'INACTIVE' %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% elif status.status == 'DISPOSED' %}
                                        <span class="badge bg-danger">مباع</span>
                                        {% elif status.status == 'DAMAGED' %}
                                        <span class="badge bg-warning">تالف</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ status.count }}</td>
                                    <td>{{ status.total_book_value|floatformat:2 }}</td>
                                    <td>
                                        {% if summary_data.total_assets > 0 %}
                                        {% widthratio status.count summary_data.total_assets 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-percentage me-2"></i>
                        نسبة الإهلاك الإجمالية
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="display-4 text-warning mb-3">
                        {% if summary_data.total_original_cost > 0 %}
                        {% widthratio summary_data.total_accumulated_depreciation summary_data.total_original_cost 100 %}%
                        {% else %}
                        0%
                        {% endif %}
                    </div>
                    <p class="text-muted">
                        {{ summary_data.total_accumulated_depreciation|floatformat:2 }} من أصل {{ summary_data.total_original_cost|floatformat:2 }}
                    </p>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-warning" 
                             role="progressbar" 
                             style="width: {% if summary_data.total_original_cost > 0 %}{% widthratio summary_data.total_accumulated_depreciation summary_data.total_original_cost 100 %}{% else %}0{% endif %}%">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات أصول ثابتة</h5>
            <p class="text-muted">لا توجد أصول ثابتة في النظام</p>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
