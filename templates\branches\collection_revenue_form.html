{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-hand-holding-usd text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'branches:collection_revenues' %}">إيرادات تحصيلية</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:collection_revenues' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات الإيراد التحصيلي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- رقم الإيراد -->
                            <div class="col-md-6 mb-3">
                                <label for="revenue_number" class="form-label">رقم الإيراد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="revenue_number" name="revenue_number" 
                                       value="CR-{{ 'now'|date:'Y' }}-" required>
                                <div class="form-text">سيتم إنشاء رقم تلقائي إذا ترك فارغاً</div>
                            </div>

                            <!-- الفرع -->
                            <div class="col-md-6 mb-3">
                                <label for="branch" class="form-label">الفرع <span class="text-danger">*</span></label>
                                <select class="form-select" id="branch" name="branch" required>
                                    <option value="">اختر الفرع</option>
                                    {% for branch in branches %}
                                    <option value="{{ branch.id }}">{{ branch.name }} ({{ branch.code }})</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- تاريخ الإيراد -->
                            <div class="col-md-6 mb-3">
                                <label for="revenue_date" class="form-label">تاريخ الإيراد <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="revenue_date" name="revenue_date" required>
                            </div>

                            <!-- المبلغ -->
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           step="0.01" min="0.01" required>
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>

                            <!-- نوع الإيراد -->
                            <div class="col-md-6 mb-3">
                                <label for="revenue_type" class="form-label">نوع الإيراد <span class="text-danger">*</span></label>
                                <select class="form-select" id="revenue_type" name="revenue_type" required>
                                    <option value="">اختر نوع الإيراد</option>
                                    <option value="SALES">مبيعات</option>
                                    <option value="SERVICES">خدمات</option>
                                    <option value="COMMISSIONS">عمولات</option>
                                    <option value="OTHER">أخرى</option>
                                </select>
                            </div>

                            <!-- اسم العميل -->
                            <div class="col-md-6 mb-3">
                                <label for="customer_name" class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                       placeholder="اسم العميل أو الجهة (اختياري)">
                            </div>

                            <!-- البيان -->
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">البيان <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="وصف تفصيلي للإيراد التحصيلي" required></textarea>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" 
                                          placeholder="ملاحظات إضافية (اختيارية)"></textarea>
                            </div>
                        </div>

                        <!-- Revenue Type Info -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>أنواع الإيرادات:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>مبيعات:</strong> إيرادات من بيع البضائع</li>
                                        <li><strong>خدمات:</strong> إيرادات من تقديم الخدمات</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>عمولات:</strong> إيرادات من العمولات</li>
                                        <li><strong>أخرى:</strong> إيرادات متنوعة أخرى</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Card -->
                        <div class="card bg-light mb-3" id="summaryCard" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">ملخص الإيراد:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>النوع:</strong> <span id="summaryType"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>المبلغ:</strong> <span id="summaryAmount"></span> ريال
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الفرع:</strong> <span id="summaryBranch"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>التاريخ:</strong> <span id="summaryDate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'branches:collection_revenues' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإيراد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('revenue_date').value = new Date().toISOString().split('T')[0];
    
    // Auto-generate revenue number
    function generateRevenueNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
        return `CR-${year}-${month}${day}-${time}`;
    }
    
    // Set auto-generated number if field is empty
    const revenueNumberInput = document.getElementById('revenue_number');
    if (!revenueNumberInput.value || revenueNumberInput.value === 'CR-{{ "now"|date:"Y" }}-') {
        revenueNumberInput.value = generateRevenueNumber();
    }
    
    // Revenue type descriptions
    const typeDescriptions = {
        'SALES': 'مبيعات',
        'SERVICES': 'خدمات',
        'COMMISSIONS': 'عمولات',
        'OTHER': 'أخرى'
    };
    
    // Update summary when form changes
    function updateSummary() {
        const type = document.getElementById('revenue_type').value;
        const amount = document.getElementById('amount').value;
        const branchSelect = document.getElementById('branch');
        const branch = branchSelect.options[branchSelect.selectedIndex].text;
        const date = document.getElementById('revenue_date').value;
        
        if (type && amount && branchSelect.value && date) {
            document.getElementById('summaryType').textContent = typeDescriptions[type];
            document.getElementById('summaryAmount').textContent = parseFloat(amount).toLocaleString();
            document.getElementById('summaryBranch').textContent = branch;
            document.getElementById('summaryDate').textContent = date;
            document.getElementById('summaryCard').style.display = 'block';
        } else {
            document.getElementById('summaryCard').style.display = 'none';
        }
    }
    
    // Add event listeners
    ['revenue_type', 'amount', 'branch', 'revenue_date'].forEach(id => {
        document.getElementById(id).addEventListener('change', updateSummary);
        document.getElementById(id).addEventListener('input', updateSummary);
    });
    
    // Auto-fill description based on revenue type and branch
    function autoFillDescription() {
        const type = document.getElementById('revenue_type').value;
        const branchSelect = document.getElementById('branch');
        const branchName = branchSelect.options[branchSelect.selectedIndex].text;
        const customerName = document.getElementById('customer_name').value;
        const descriptionField = document.getElementById('description');
        
        if (type && branchSelect.value && !descriptionField.value) {
            let description = `إيراد ${typeDescriptions[type]} - فرع ${branchName}`;
            if (customerName) {
                description += ` - العميل: ${customerName}`;
            }
            descriptionField.value = description;
        }
    }
    
    ['revenue_type', 'branch', 'customer_name'].forEach(id => {
        document.getElementById(id).addEventListener('change', autoFillDescription);
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount').value);
        if (amount <= 0) {
            alert('يجب أن يكون المبلغ أكبر من صفر');
            e.preventDefault();
            return;
        }
        
        const description = document.getElementById('description').value.trim();
        if (description.length < 10) {
            alert('يجب أن يكون البيان أكثر تفصيلاً (على الأقل 10 أحرف)');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}
