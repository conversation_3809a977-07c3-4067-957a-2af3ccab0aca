{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-building text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'branches:branch_list' %}">تعريف الفروع</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:branch_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات الفرع
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- اسم الفرع -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{% if branch %}{{ branch.name }}{% elif form_data %}{{ form_data.name }}{% endif %}" 
                                       required>
                            </div>

                            <!-- كود الفرع -->
                            <div class="col-md-6 mb-3">
                                <label for="code" class="form-label">كود الفرع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="code" name="code" 
                                       value="{% if branch %}{{ branch.code }}{% elif form_data %}{{ form_data.code }}{% endif %}" 
                                       required>
                                <div class="form-text">كود فريد للفرع</div>
                            </div>

                            <!-- اسم المدير -->
                            <div class="col-md-6 mb-3">
                                <label for="manager_name" class="form-label">اسم المدير</label>
                                <input type="text" class="form-control" id="manager_name" name="manager_name" 
                                       value="{% if branch %}{{ branch.manager_name }}{% elif form_data %}{{ form_data.manager_name }}{% endif %}">
                            </div>

                            <!-- الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{% if branch %}{{ branch.phone }}{% elif form_data %}{{ form_data.phone }}{% endif %}">
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-12 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{% if branch %}{{ branch.email }}{% elif form_data %}{{ form_data.email }}{% endif %}">
                            </div>

                            <!-- العنوان -->
                            <div class="col-md-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{% if branch %}{{ branch.address }}{% elif form_data %}{{ form_data.address }}{% endif %}</textarea>
                            </div>

                            <!-- الحالة (فقط في التعديل) -->
                            {% if branch %}
                            <div class="col-md-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if branch.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        فرع نشط
                                    </label>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'branches:branch_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if branch %}تحديث{% else %}حفظ{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate code from name if adding new branch
    {% if not branch %}
    const nameInput = document.getElementById('name');
    const codeInput = document.getElementById('code');
    
    nameInput.addEventListener('input', function() {
        if (!codeInput.value) {
            // Generate code from name (first 3 letters + random number)
            const name = this.value.trim();
            if (name.length >= 3) {
                const code = name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 1000);
                codeInput.value = code;
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}
