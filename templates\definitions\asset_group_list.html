{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">
            <i class="fas fa-layer-group me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'definitions:asset_group_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة مجموعة جديدة
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="البحث في مجموعات الأصول">
                </div>
            </div>
            <div class="col-md-3">
                <select name="category" class="form-select">
                    <option value="">جميع الفئات</option>
                    <option value="BUILDING" {% if category_filter == 'BUILDING' %}selected{% endif %}>مباني</option>
                    <option value="MACHINERY" {% if category_filter == 'MACHINERY' %}selected{% endif %}>آلات ومعدات</option>
                    <option value="VEHICLE" {% if category_filter == 'VEHICLE' %}selected{% endif %}>مركبات</option>
                    <option value="FURNITURE" {% if category_filter == 'FURNITURE' %}selected{% endif %}>أثاث ومفروشات</option>
                    <option value="COMPUTER" {% if category_filter == 'COMPUTER' %}selected{% endif %}>أجهزة حاسوب</option>
                    <option value="LAND" {% if category_filter == 'LAND' %}selected{% endif %}>أراضي</option>
                    <option value="INTANGIBLE" {% if category_filter == 'INTANGIBLE' %}selected{% endif %}>أصول معنوية</option>
                    <option value="OTHER" {% if category_filter == 'OTHER' %}selected{% endif %}>أخرى</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="parent" class="form-select">
                    <option value="">جميع المجموعات</option>
                    {% for parent in parent_groups %}
                        <option value="{{ parent.pk }}" {% if parent_filter == parent.pk|stringformat:"s" %}selected{% endif %}>
                            {{ parent.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <div class="d-flex gap-1">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{% url 'definitions:asset_group_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Asset Groups Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة مجموعات الأصول
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>كود المجموعة</th>
                            <th>اسم المجموعة</th>
                            <th>المجموعة الأب</th>
                            <th>فئة الأصل</th>
                            <th>طريقة الاستهلاك</th>
                            <th>العمر الافتراضي</th>
                            <th>قابل للاستهلاك</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for asset_group in page_obj %}
                        <tr>
                            <td><strong>{{ asset_group.code }}</strong></td>
                            <td>
                                <div>
                                    {{ asset_group.name }}
                                    {% if asset_group.level > 0 %}
                                        <br>
                                        <small class="text-muted">
                                            {% for i in "x"|ljust:asset_group.level %}
                                                &nbsp;&nbsp;&nbsp;&nbsp;
                                            {% endfor %}
                                            └─ مستوى {{ asset_group.level }}
                                        </small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if asset_group.parent %}
                                    <span class="badge bg-info">{{ asset_group.parent.name }}</span>
                                {% else %}
                                    <span class="text-muted">مجموعة رئيسية</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if asset_group.asset_category == 'BUILDING' %}
                                    <span class="badge bg-primary">مباني</span>
                                {% elif asset_group.asset_category == 'MACHINERY' %}
                                    <span class="badge bg-success">آلات ومعدات</span>
                                {% elif asset_group.asset_category == 'VEHICLE' %}
                                    <span class="badge bg-warning">مركبات</span>
                                {% elif asset_group.asset_category == 'FURNITURE' %}
                                    <span class="badge bg-info">أثاث ومفروشات</span>
                                {% elif asset_group.asset_category == 'COMPUTER' %}
                                    <span class="badge bg-secondary">أجهزة حاسوب</span>
                                {% elif asset_group.asset_category == 'LAND' %}
                                    <span class="badge bg-dark">أراضي</span>
                                {% elif asset_group.asset_category == 'INTANGIBLE' %}
                                    <span class="badge bg-purple">أصول معنوية</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">أخرى</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if asset_group.depreciation_method == 'STRAIGHT_LINE' %}
                                    القسط الثابت
                                {% elif asset_group.depreciation_method == 'DECLINING_BALANCE' %}
                                    الرصيد المتناقص
                                {% elif asset_group.depreciation_method == 'UNITS_OF_PRODUCTION' %}
                                    وحدات الإنتاج
                                {% else %}
                                    مجموع سنوات الخدمة
                                {% endif %}
                            </td>
                            <td>
                                {% if asset_group.default_useful_life %}
                                    {{ asset_group.default_useful_life }} سنة
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if asset_group.is_depreciable %}
                                    <i class="fas fa-check text-success" title="قابل للاستهلاك"></i>
                                {% else %}
                                    <i class="fas fa-times text-muted" title="غير قابل للاستهلاك"></i>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'definitions:asset_group_edit' asset_group.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ asset_group.pk }}"
                                            data-name="{{ asset_group.name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مجموعات أصول</h5>
                <p class="text-muted">لم يتم العثور على أي مجموعات أصول مطابقة لمعايير البحث</p>
                <a href="{% url 'definitions:asset_group_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مجموعة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف مجموعة الأصول: <strong id="delete-item-name"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    const deleteItemName = document.getElementById('delete-item-name');
    let deleteId = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteId = this.dataset.id;
            deleteItemName.textContent = this.dataset.name;
            deleteModal.show();
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        if (deleteId) {
            fetch(`/definitions/asset-groups/${deleteId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
            });
        }
        deleteModal.hide();
    });
});
</script>

<style>
.bg-purple { background-color: #6f42c1 !important; }
</style>
{% endblock %}
