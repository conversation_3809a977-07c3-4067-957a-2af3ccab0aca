{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-university text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item active">إيداعات من بنوك الفروع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'branches:bank_deposits_from_branch_banks_add' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة إيداع من بنك فرع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_movements }}</h4>
                            <p class="mb-0">إجمالي التحويلات</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_amount|floatformat:2 }}</h4>
                            <p class="mb-0">إجمالي المبلغ المحول</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-university"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control" placeholder="البحث في رقم الحركة أو البنك..." value="{{ search }}">
                </div>
                <div class="col-md-3">
                    <select name="branch_id" class="form-select">
                        <option value="">جميع الفروع</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if branch_filter == branch.id|stringformat:"s" %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}" placeholder="من تاريخ">
                </div>
                <div class="col-md-2">
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}" placeholder="إلى تاريخ">
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bank Movements List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                التحويلات البنكية من بنوك الفروع
                <span class="badge bg-info">{{ total_movements }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الحركة</th>
                            <th>التاريخ</th>
                            <th>الفرع</th>
                            <th>بنك الفرع</th>
                            <th>رقم الحساب</th>
                            <th>المبلغ</th>
                            <th>البيان</th>
                            <th>المستخدم</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ movement.movement_number }}</strong>
                            </td>
                            <td>{{ movement.movement_date|date:"Y-m-d" }}</td>
                            <td>
                                <strong>{{ movement.branch.name }}</strong>
                                <small class="text-muted d-block">{{ movement.branch.code }}</small>
                            </td>
                            <td>
                                <strong>{{ movement.bank_name }}</strong>
                            </td>
                            <td>
                                {% if movement.account_number %}
                                {{ movement.account_number }}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-info">{{ movement.amount|floatformat:2 }}</strong>
                            </td>
                            <td>{{ movement.description|truncatechars:40 }}</td>
                            <td>
                                <small class="text-muted">{{ movement.created_by.username }}</small>
                                <small class="text-muted d-block">{{ movement.created_at|date:"Y-m-d H:i" }}</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5">الإجمالي</th>
                            <th class="text-info">{{ total_amount|floatformat:2 }}</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">السابق</a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if branch_filter %}&branch_id={{ branch_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-university fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تحويلات بنكية</h5>
                <p class="text-muted">لم يتم العثور على أي تحويلات بنكية من بنوك الفروع</p>
                <a href="{% url 'branches:bank_deposits_from_branch_banks_add' %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    تسجيل تحويل بنكي جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
