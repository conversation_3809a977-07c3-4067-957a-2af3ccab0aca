# Generated by Django 5.2.2 on 2025-06-06 14:18

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0005_expensecategory_expenseitem_revenuecategory_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('alert_type', models.CharField(choices=[('EXPENSE_LIMIT', 'تجاوز حد المصروف'), ('RECURRING_DUE', 'مصروف دوري مستحق'), ('APPROVAL_REQUIRED', 'يتطلب موافقة'), ('DOCUMENT_MISSING', 'مستند مفقود'), ('REVENUE_TARGET', 'هدف إيراد'), ('TAX_CALCULATION', 'حساب ضريبة')], max_length=20, verbose_name='نوع التنبيه')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('priority', models.CharField(choices=[('LOW', 'منخفض'), ('MEDIUM', 'متوسط'), ('HIGH', 'عالي'), ('URGENT', 'عاجل')], default='MEDIUM', max_length=10, verbose_name='مستوى الأولوية')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('ACKNOWLEDGED', 'تم الاطلاع'), ('RESOLVED', 'تم الحل'), ('DISMISSED', 'تم التجاهل')], default='PENDING', max_length=15, verbose_name='حالة التنبيه')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='المبلغ')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('expense_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.expensecategory', verbose_name='فئة المصروف')),
                ('expense_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.expenseitem', verbose_name='بند المصروف')),
                ('revenue_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.revenuecategory', verbose_name='فئة الإيراد')),
                ('revenue_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.revenueitem', verbose_name='بند الإيراد')),
                ('target_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='financial_alerts', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم المستهدف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تنبيه مالي',
                'verbose_name_plural': 'التنبيهات المالية',
                'ordering': ['-created_at', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='FinishedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('name_english', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('brand', models.CharField(blank=True, max_length=100, verbose_name='العلامة التجارية')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('version', models.CharField(blank=True, max_length=50, verbose_name='الإصدار')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='الباركود')),
                ('sku', models.CharField(blank=True, max_length=100, verbose_name='رقم المنتج')),
                ('specifications', models.TextField(blank=True, verbose_name='المواصفات الفنية')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن (كجم)')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('color', models.CharField(blank=True, max_length=50, verbose_name='اللون')),
                ('material', models.CharField(blank=True, max_length=100, verbose_name='المادة')),
                ('standard_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة المعيارية')),
                ('material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة المواد')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف الإضافية')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر الجملة')),
                ('retail_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر التجزئة')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('max_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='نقطة إعادة الطلب')),
                ('production_lead_time', models.IntegerField(blank=True, null=True, verbose_name='مدة الإنتاج (أيام)')),
                ('batch_size', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='حجم الدفعة')),
                ('quality_grade', models.CharField(blank=True, choices=[('A', 'ممتاز'), ('B', 'جيد جداً'), ('C', 'جيد'), ('D', 'مقبول')], max_length=20, verbose_name='درجة الجودة')),
                ('shelf_life_days', models.IntegerField(blank=True, null=True, verbose_name='مدة الصلاحية (أيام)')),
                ('certifications', models.TextField(blank=True, verbose_name='الشهادات والمعايير')),
                ('compliance_standards', models.TextField(blank=True, verbose_name='معايير الامتثال')),
                ('packaging_type', models.CharField(blank=True, max_length=100, verbose_name='نوع التعبئة')),
                ('package_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='وزن العبوة (كجم)')),
                ('package_dimensions', models.CharField(blank=True, max_length=100, verbose_name='أبعاد العبوة')),
                ('units_per_package', models.IntegerField(blank=True, null=True, verbose_name='الوحدات في العبوة')),
                ('inventory_account', models.CharField(blank=True, max_length=20, verbose_name='حساب المخزون')),
                ('cogs_account', models.CharField(blank=True, max_length=20, verbose_name='حساب تكلفة البضاعة المباعة')),
                ('revenue_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الإيرادات')),
                ('is_manufactured', models.BooleanField(default=True, verbose_name='منتج مصنع')),
                ('is_sellable', models.BooleanField(default=True, verbose_name='قابل للبيع')),
                ('is_purchasable', models.BooleanField(default=False, verbose_name='قابل للشراء')),
                ('track_serial_numbers', models.BooleanField(default=False, verbose_name='تتبع الأرقام التسلسلية')),
                ('track_lot_numbers', models.BooleanField(default=False, verbose_name='تتبع أرقام الدفعات')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='صورة المنتج')),
                ('technical_drawing', models.FileField(blank=True, null=True, upload_to='products/drawings/', verbose_name='الرسم الفني')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.itemcategory', verbose_name='فئة المنتج')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة القياس')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'منتج تام',
                'verbose_name_plural': 'المنتجات التامة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود مسار الإنتاج')),
                ('name', models.CharField(max_length=100, verbose_name='اسم مسار الإنتاج')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('version', models.CharField(default='1.0', max_length=20, verbose_name='إصدار المسار')),
                ('is_default', models.BooleanField(default=True, verbose_name='المسار الافتراضي')),
                ('total_lead_time_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='إجمالي وقت الإنتاج (ساعات)')),
                ('efficiency_rate', models.DecimalField(decimal_places=2, default=100.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الكفاءة (%)')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_production_routes', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('finished_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_routes', to='definitions.finishedproduct', verbose_name='المنتج التام')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مسار إنتاج',
                'verbose_name_plural': 'مسارات الإنتاج',
                'ordering': ['finished_product', 'version'],
                'unique_together': {('finished_product', 'version')},
            },
        ),
        migrations.CreateModel(
            name='ProductionStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المرحلة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المرحلة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('sequence_number', models.IntegerField(verbose_name='رقم التسلسل')),
                ('stage_type', models.CharField(choices=[('PREPARATION', 'تحضير'), ('PROCESSING', 'تشغيل'), ('ASSEMBLY', 'تجميع'), ('TESTING', 'اختبار'), ('PACKAGING', 'تعبئة'), ('QUALITY_CONTROL', 'مراقبة جودة'), ('FINISHING', 'تشطيب'), ('OTHER', 'أخرى')], default='PROCESSING', max_length=20, verbose_name='نوع المرحلة')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('required_equipment', models.TextField(blank=True, verbose_name='المعدات المطلوبة')),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='المدة المقدرة (ساعات)')),
                ('setup_time_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='وقت الإعداد (ساعات)')),
                ('labor_cost_per_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة العمالة/ساعة')),
                ('overhead_cost_per_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكاليف الإضافية/ساعة')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='تتطلب موافقة')),
                ('requires_quality_check', models.BooleanField(default=True, verbose_name='تتطلب فحص جودة')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مرحلة حرجة')),
                ('can_run_parallel', models.BooleanField(default=False, verbose_name='يمكن تشغيلها بالتوازي')),
                ('quality_standards', models.TextField(blank=True, verbose_name='معايير الجودة')),
                ('acceptance_criteria', models.TextField(blank=True, verbose_name='معايير القبول')),
                ('cost_center', models.CharField(blank=True, max_length=20, verbose_name='مركز التكلفة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('responsible_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='production_stages', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مرحلة إنتاج',
                'verbose_name_plural': 'مراحل الإنتاج',
                'ordering': ['sequence_number', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionStageRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sequence_number', models.IntegerField(verbose_name='رقم التسلسل')),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='المدة المقدرة (ساعات)')),
                ('setup_time_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='وقت الإعداد (ساعات)')),
                ('labor_cost_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة العمالة المخصصة')),
                ('overhead_cost_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكاليف الإضافية المخصصة')),
                ('is_optional', models.BooleanField(default=False, verbose_name='مرحلة اختيارية')),
                ('is_parallel', models.BooleanField(default=False, verbose_name='يمكن تشغيلها بالتوازي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('prerequisite_stages', models.ManyToManyField(blank=True, to='definitions.productionstageroute', verbose_name='المراحل المطلوبة مسبقاً')),
                ('production_route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stage_routes', to='definitions.productionroute', verbose_name='مسار الإنتاج')),
                ('production_stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productionstage', verbose_name='مرحلة الإنتاج')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مرحلة مسار الإنتاج',
                'verbose_name_plural': 'مراحل مسارات الإنتاج',
                'ordering': ['production_route', 'sequence_number'],
                'unique_together': {('production_route', 'production_stage')},
            },
        ),
    ]
