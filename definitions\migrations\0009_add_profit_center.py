# Generated by Django 5.2.2 on 2025-06-06 15:11

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0008_add_production_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProfitCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود مركز الربحية')),
                ('name', models.CharField(max_length=100, verbose_name='اسم مركز الربحية')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('level', models.IntegerField(default=1, verbose_name='المستوى')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('target_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='هدف الإيرادات السنوي')),
                ('target_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='هدف الربح السنوي')),
                ('target_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='هدف هامش الربح (%)')),
                ('evaluation_period', models.CharField(choices=[('MONTHLY', 'شهري'), ('QUARTERLY', 'ربع سنوي'), ('SEMI_ANNUAL', 'نصف سنوي'), ('ANNUAL', 'سنوي')], default='QUARTERLY', max_length=20, verbose_name='فترة التقييم')),
                ('revenue_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الإيرادات')),
                ('expense_account', models.CharField(blank=True, max_length=20, verbose_name='حساب المصروفات')),
                ('asset_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الأصول')),
                ('allocate_overhead', models.BooleanField(default=True, verbose_name='توزيع التكاليف الإضافية')),
                ('overhead_allocation_method', models.CharField(choices=[('REVENUE_BASED', 'على أساس الإيرادات'), ('EMPLOYEE_BASED', 'على أساس عدد الموظفين'), ('ASSET_BASED', 'على أساس الأصول'), ('EQUAL', 'توزيع متساوي'), ('CUSTOM', 'طريقة مخصصة')], default='REVENUE_BASED', max_length=20, verbose_name='طريقة توزيع التكاليف')),
                ('overhead_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة التكاليف الإضافية (%)')),
                ('include_in_reports', models.BooleanField(default=True, verbose_name='تضمين في التقارير')),
                ('consolidate_children', models.BooleanField(default=True, verbose_name='دمج البيانات من المراكز الفرعية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_profit_centers', to=settings.AUTH_USER_MODEL, verbose_name='المدير المسؤول')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='definitions.profitcenter', verbose_name='مركز الربحية الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مركز ربحية',
                'verbose_name_plural': 'مراكز الربحية',
                'ordering': ['level', 'code', 'name'],
            },
        ),
    ]
