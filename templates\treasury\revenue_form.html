{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-plus-circle text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'treasury:revenue_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الإيراد الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الإيراد الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.revenue_number.id_for_label }}" class="form-label">{{ form.revenue_number.label }}</label>
                                {{ form.revenue_number }}
                                {% if form.revenue_number.errors %}
                                    <div class="text-danger small">{{ form.revenue_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.treasury.id_for_label }}" class="form-label">{{ form.treasury.label }}</label>
                                {{ form.treasury }}
                                {% if form.treasury.errors %}
                                    <div class="text-danger small">{{ form.treasury.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.revenue_type.id_for_label }}" class="form-label">{{ form.revenue_type.label }}</label>
                                {{ form.revenue_type }}
                                {% if form.revenue_type.errors %}
                                    <div class="text-danger small">{{ form.revenue_type.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">
                                    <a href="{% url 'treasury:revenue_type_create' %}" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة نوع إيراد جديد
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">{{ form.amount.label }}</label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger small">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.source.id_for_label }}" class="form-label">{{ form.source.label }}</label>
                                {{ form.source }}
                                {% if form.source.errors %}
                                    <div class="text-danger small">{{ form.source.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات مهمة -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات مهمة حول الإيرادات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        أنواع الإيرادات
                                    </h6>
                                    <ul class="mb-0">
                                        <li>إيرادات تشغيلية (مبيعات، خدمات)</li>
                                        <li>إيرادات استثمارية (فوائد، أرباح)</li>
                                        <li>إيرادات أخرى (إيجارات، عمولات)</li>
                                        <li>إيرادات رأسمالية (بيع أصول)</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-file-alt me-2"></i>
                                        توثيق الإيرادات
                                    </h6>
                                    <ul class="mb-0">
                                        <li>تحديد مصدر الإيراد بدقة</li>
                                        <li>كتابة وصف واضح للإيراد</li>
                                        <li>الاحتفاظ بالفواتير والمستندات</li>
                                        <li>تصنيف الإيراد حسب النوع</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'treasury:revenue_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحقق من صحة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(document.querySelector('input[name="amount"]').value);
            const revenueType = document.querySelector('select[name="revenue_type"]').value;
            const treasury = document.querySelector('select[name="treasury"]').value;
            const description = document.querySelector('input[name="description"]').value.trim();
            const source = document.querySelector('input[name="source"]').value.trim();
            
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('يجب إدخال مبلغ صحيح');
                return false;
            }
            
            if (!revenueType) {
                e.preventDefault();
                alert('يجب اختيار نوع الإيراد');
                return false;
            }
            
            if (!treasury) {
                e.preventDefault();
                alert('يجب اختيار الخزينة');
                return false;
            }
            
            if (!description) {
                e.preventDefault();
                alert('يجب إدخال بيان الإيراد');
                return false;
            }
            
            if (!source) {
                e.preventDefault();
                alert('يجب تحديد مصدر الإيراد');
                return false;
            }
        });
    }
    
    // تحديث البيان تلقائياً عند اختيار نوع الإيراد
    const revenueTypeField = document.querySelector('select[name="revenue_type"]');
    const descriptionField = document.querySelector('input[name="description"]');
    
    if (revenueTypeField && descriptionField) {
        revenueTypeField.addEventListener('change', function() {
            if (this.value && !descriptionField.value) {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.text) {
                    descriptionField.value = selectedOption.text;
                }
            }
        });
    }
});
</script>
{% endblock %}
