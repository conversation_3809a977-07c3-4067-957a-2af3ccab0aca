# Generated by Django 5.2.2 on 2025-06-06 14:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0007_delete_production_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinishedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('name_english', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('brand', models.CharField(blank=True, max_length=100, verbose_name='العلامة التجارية')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('version', models.CharField(blank=True, max_length=50, verbose_name='الإصدار')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='الباركود')),
                ('sku', models.CharField(blank=True, max_length=100, verbose_name='رقم المنتج')),
                ('specifications', models.TextField(blank=True, verbose_name='المواصفات الفنية')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن (كجم)')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('color', models.CharField(blank=True, max_length=50, verbose_name='اللون')),
                ('material', models.CharField(blank=True, max_length=100, verbose_name='المادة')),
                ('standard_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة المعيارية')),
                ('material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة المواد')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف الإضافية')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر الجملة')),
                ('retail_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر التجزئة')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('max_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='نقطة إعادة الطلب')),
                ('production_lead_time', models.IntegerField(blank=True, null=True, verbose_name='مدة الإنتاج (أيام)')),
                ('batch_size', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='حجم الدفعة')),
                ('quality_grade', models.CharField(blank=True, choices=[('A', 'ممتاز'), ('B', 'جيد جداً'), ('C', 'جيد'), ('D', 'مقبول')], max_length=20, verbose_name='درجة الجودة')),
                ('shelf_life_days', models.IntegerField(blank=True, null=True, verbose_name='مدة الصلاحية (أيام)')),
                ('certifications', models.TextField(blank=True, verbose_name='الشهادات والمعايير')),
                ('compliance_standards', models.TextField(blank=True, verbose_name='معايير الامتثال')),
                ('packaging_type', models.CharField(blank=True, max_length=100, verbose_name='نوع التعبئة')),
                ('package_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='وزن العبوة (كجم)')),
                ('package_dimensions', models.CharField(blank=True, max_length=100, verbose_name='أبعاد العبوة')),
                ('units_per_package', models.IntegerField(blank=True, null=True, verbose_name='الوحدات في العبوة')),
                ('inventory_account', models.CharField(blank=True, max_length=20, verbose_name='حساب المخزون')),
                ('cogs_account', models.CharField(blank=True, max_length=20, verbose_name='حساب تكلفة البضاعة المباعة')),
                ('revenue_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الإيرادات')),
                ('is_manufactured', models.BooleanField(default=True, verbose_name='منتج مصنع')),
                ('is_sellable', models.BooleanField(default=True, verbose_name='قابل للبيع')),
                ('is_purchasable', models.BooleanField(default=False, verbose_name='قابل للشراء')),
                ('track_serial_numbers', models.BooleanField(default=False, verbose_name='تتبع الأرقام التسلسلية')),
                ('track_lot_numbers', models.BooleanField(default=False, verbose_name='تتبع أرقام الدفعات')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='صورة المنتج')),
                ('technical_drawing', models.FileField(blank=True, null=True, upload_to='products/drawings/', verbose_name='الرسم الفني')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.itemcategory', verbose_name='فئة المنتج')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة القياس')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'منتج تام',
                'verbose_name_plural': 'المنتجات التامة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المرحلة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المرحلة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('sequence_number', models.IntegerField(verbose_name='رقم التسلسل')),
                ('stage_type', models.CharField(choices=[('PREPARATION', 'تحضير'), ('PROCESSING', 'تشغيل'), ('ASSEMBLY', 'تجميع'), ('TESTING', 'اختبار'), ('PACKAGING', 'تعبئة'), ('QUALITY_CONTROL', 'مراقبة جودة'), ('FINISHING', 'تشطيب'), ('OTHER', 'أخرى')], default='PROCESSING', max_length=20, verbose_name='نوع المرحلة')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('required_equipment', models.TextField(blank=True, verbose_name='المعدات المطلوبة')),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='المدة المقدرة (ساعات)')),
                ('setup_time_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='وقت الإعداد (ساعات)')),
                ('labor_cost_per_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة العمالة/ساعة')),
                ('overhead_cost_per_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكاليف الإضافية/ساعة')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='تتطلب موافقة')),
                ('requires_quality_check', models.BooleanField(default=True, verbose_name='تتطلب فحص جودة')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مرحلة حرجة')),
                ('can_run_parallel', models.BooleanField(default=False, verbose_name='يمكن تشغيلها بالتوازي')),
                ('quality_standards', models.TextField(blank=True, verbose_name='معايير الجودة')),
                ('acceptance_criteria', models.TextField(blank=True, verbose_name='معايير القبول')),
                ('cost_center', models.CharField(blank=True, max_length=20, verbose_name='مركز التكلفة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('responsible_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='production_stages', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مرحلة إنتاج',
                'verbose_name_plural': 'مراحل الإنتاج',
                'ordering': ['sequence_number', 'name'],
            },
        ),
    ]
