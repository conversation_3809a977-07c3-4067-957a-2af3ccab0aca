{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-industry text-primary me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات أمر الإنتاج الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات أمر الإنتاج الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.order_number.id_for_label }}" class="form-label">{{ form.order_number.label }}</label>
                                {{ form.order_number }}
                                {% if form.order_number.errors %}
                                    <div class="text-danger small">{{ form.order_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.product_item.id_for_label }}" class="form-label">{{ form.product_item.label }}</label>
                                {{ form.product_item }}
                                {% if form.product_item.errors %}
                                    <div class="text-danger small">{{ form.product_item.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.quantity_to_produce.id_for_label }}" class="form-label">{{ form.quantity_to_produce.label }}</label>
                                {{ form.quantity_to_produce }}
                                {% if form.quantity_to_produce.errors %}
                                    <div class="text-danger small">{{ form.quantity_to_produce.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.production_cost_per_unit.id_for_label }}" class="form-label">{{ form.production_cost_per_unit.label }}</label>
                                {{ form.production_cost_per_unit }}
                                {% if form.production_cost_per_unit.errors %}
                                    <div class="text-danger small">{{ form.production_cost_per_unit.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="text-danger small">{{ form.priority.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.expected_completion_date.id_for_label }}" class="form-label">{{ form.expected_completion_date.label }}</label>
                                {{ form.expected_completion_date }}
                                {% if form.expected_completion_date.errors %}
                                    <div class="text-danger small">{{ form.expected_completion_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المواد الخام المطلوبة -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes text-warning me-2"></i>
                            المواد الخام المطلوبة
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="addMaterial()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مادة خام
                        </button>
                    </div>
                    <div class="card-body">
                        {{ material_formset.management_form }}
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="materials-table">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 25%">المادة الخام</th>
                                        <th style="width: 15%">الكمية المطلوبة</th>
                                        <th style="width: 15%">تكلفة الوحدة</th>
                                        <th style="width: 15%">الإجمالي</th>
                                        <th style="width: 10%">تاريخ الانتهاء</th>
                                        <th style="width: 10%">رقم الدفعة</th>
                                        <th style="width: 5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for form in material_formset %}
                                        <tr class="material-row">
                                            <td>
                                                {{ form.material }}
                                                {% if form.material.errors %}
                                                    <div class="text-danger small">{{ form.material.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.quantity_required }}
                                                {% if form.quantity_required.errors %}
                                                    <div class="text-danger small">{{ form.quantity_required.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.unit_cost }}
                                                {% if form.unit_cost.errors %}
                                                    <div class="text-danger small">{{ form.unit_cost.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <input type="text" class="form-control total-value" readonly>
                                            </td>
                                            <td>
                                                {{ form.expiry_date }}
                                                {% if form.expiry_date.errors %}
                                                    <div class="text-danger small">{{ form.expiry_date.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.batch_number }}
                                                {% if form.batch_number.errors %}
                                                    <div class="text-danger small">{{ form.batch_number.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if form.DELETE %}
                                                    {{ form.DELETE }}
                                                {% endif %}
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMaterial(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                            {% for hidden in form.hidden_fields %}
                                                {{ hidden }}
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-warning">
                                        <td colspan="3"><strong>إجمالي تكلفة المواد:</strong></td>
                                        <td><strong id="materials-total">0.00 ر.س</strong></td>
                                        <td colspan="3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص التكاليف -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص التكاليف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted">إجمالي تكلفة المواد</h6>
                                    <h4 class="text-warning" id="summary-materials">0.00 ر.س</h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted">تكلفة الإنتاج</h6>
                                    <h4 class="text-info" id="summary-production">0.00 ر.س</h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted">إجمالي التكلفة</h6>
                                    <h4 class="text-primary" id="summary-total">0.00 ر.س</h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted">تكلفة الوحدة</h6>
                                    <h4 class="text-success" id="summary-unit-cost">0.00 ر.س</h4>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تنبيه:</strong> سيتم خصم المواد الخام من المخزون عند بدء الإنتاج وإضافة المنتج النهائي عند الإكمال.
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let materialIndex = {{ material_formset.total_form_count }};

function addMaterial() {
    const tbody = document.querySelector('#materials-table tbody');
    const newRow = document.createElement('tr');
    newRow.className = 'material-row';
    newRow.innerHTML = `
        <td>
            <select name="materials-${materialIndex}-material" class="form-select" required>
                <option value="">اختر المادة الخام</option>
            </select>
        </td>
        <td>
            <input type="number" name="materials-${materialIndex}-quantity_required" class="form-control quantity" step="0.001" min="0.001" required>
        </td>
        <td>
            <input type="number" name="materials-${materialIndex}-unit_cost" class="form-control unit-cost" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control total-value" readonly>
        </td>
        <td>
            <input type="date" name="materials-${materialIndex}-expiry_date" class="form-control">
        </td>
        <td>
            <input type="text" name="materials-${materialIndex}-batch_number" class="form-control">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMaterial(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    
    materialIndex++;
    document.querySelector('#id_materials-TOTAL_FORMS').value = materialIndex;
    addEventListeners(newRow);
}

function removeMaterial(button) {
    const row = button.closest('tr');
    const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
    
    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        row.style.display = 'none';
    } else {
        row.remove();
        materialIndex--;
        document.querySelector('#id_materials-TOTAL_FORMS').value = materialIndex;
    }
    
    calculateTotals();
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const unitCost = parseFloat(row.querySelector('.unit-cost').value) || 0;
    const total = quantity * unitCost;
    
    row.querySelector('.total-value').value = total.toFixed(2);
    calculateTotals();
}

function calculateTotals() {
    // حساب إجمالي تكلفة المواد
    let materialsTotal = 0;
    document.querySelectorAll('.material-row:not([style*="display: none"]) .total-value').forEach(input => {
        materialsTotal += parseFloat(input.value) || 0;
    });
    
    // تكلفة الإنتاج
    const quantityToProduce = parseFloat(document.querySelector('input[name="quantity_to_produce"]').value) || 0;
    const productionCostPerUnit = parseFloat(document.querySelector('input[name="production_cost_per_unit"]').value) || 0;
    const productionTotal = quantityToProduce * productionCostPerUnit;
    
    // إجمالي التكلفة
    const totalCost = materialsTotal + productionTotal;
    
    // تكلفة الوحدة
    const unitCost = quantityToProduce > 0 ? totalCost / quantityToProduce : 0;
    
    // تحديث العرض
    document.getElementById('materials-total').textContent = materialsTotal.toFixed(2) + ' ر.س';
    document.getElementById('summary-materials').textContent = materialsTotal.toFixed(2) + ' ر.س';
    document.getElementById('summary-production').textContent = productionTotal.toFixed(2) + ' ر.س';
    document.getElementById('summary-total').textContent = totalCost.toFixed(2) + ' ر.س';
    document.getElementById('summary-unit-cost').textContent = unitCost.toFixed(2) + ' ر.س';
}

function addEventListeners(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitCostInput = row.querySelector('.unit-cost');
    
    if (quantityInput) {
        quantityInput.addEventListener('input', () => calculateRowTotal(row));
    }
    if (unitCostInput) {
        unitCostInput.addEventListener('input', () => calculateRowTotal(row));
    }
}

// إضافة event listeners للصفوف الموجودة
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.material-row').forEach(addEventListeners);
    
    // إضافة listeners لحقول الإنتاج
    const quantityInput = document.querySelector('input[name="quantity_to_produce"]');
    const productionCostInput = document.querySelector('input[name="production_cost_per_unit"]');
    
    if (quantityInput) {
        quantityInput.addEventListener('input', calculateTotals);
    }
    if (productionCostInput) {
        productionCostInput.addEventListener('input', calculateTotals);
    }
    
    calculateTotals();
    
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
    
    // تحديد تاريخ الإنجاز المتوقع (أسبوع من اليوم)
    const expectedDateField = document.querySelector('input[type="date"][name="expected_completion_date"]');
    if (expectedDateField && !expectedDateField.value) {
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        expectedDateField.value = nextWeek.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
