from .models import TaskbarSettings, LicenseInfo


def taskbar_settings(request):
    """Context processor لإعدادات شريط المهام"""
    context = {}
    
    if request.user.is_authenticated:
        try:
            settings = TaskbarSettings.objects.get(user=request.user)
            context['user_taskbar_settings'] = settings
        except TaskbarSettings.DoesNotExist:
            context['user_taskbar_settings'] = None
    
    return context


def license_info(request):
    """Context processor لمعلومات الترخيص"""
    context = {}
    
    try:
        license_obj = LicenseInfo.objects.first()
        context['system_license'] = license_obj
    except LicenseInfo.DoesNotExist:
        context['system_license'] = None
    
    return context
