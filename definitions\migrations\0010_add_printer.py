# Generated by Django 5.2.2 on 2025-06-06 15:37

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0009_add_profit_center'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Printer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الطابعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الطابعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('brand', models.CharField(blank=True, max_length=50, verbose_name='العلامة التجارية')),
                ('model', models.CharField(blank=True, max_length=50, verbose_name='الموديل')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('printer_type', models.CharField(choices=[('THERMAL', 'حرارية'), ('INKJET', 'نفث الحبر'), ('LASER', 'ليزر'), ('DOT_MATRIX', 'نقطية'), ('LABEL', 'ملصقات'), ('RECEIPT', 'فواتير'), ('BARCODE', 'باركود'), ('PHOTO', 'صور'), ('OTHER', 'أخرى')], default='THERMAL', max_length=20, verbose_name='نوع الطابعة')),
                ('connection_type', models.CharField(choices=[('USB', 'USB'), ('NETWORK', 'شبكة'), ('BLUETOOTH', 'بلوتوث'), ('WIFI', 'واي فاي'), ('SERIAL', 'تسلسلي'), ('PARALLEL', 'متوازي')], default='USB', max_length=20, verbose_name='نوع الاتصال')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('port', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(65535)], verbose_name='رقم المنفذ')),
                ('paper_size', models.CharField(choices=[('A4', 'A4'), ('A5', 'A5'), ('LETTER', 'Letter'), ('RECEIPT_80MM', 'فاتورة 80 مم'), ('RECEIPT_58MM', 'فاتورة 58 مم'), ('LABEL_4X6', 'ملصق 4×6'), ('CUSTOM', 'مخصص')], default='A4', max_length=20, verbose_name='حجم الورق')),
                ('paper_width', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='عرض الورق (مم)')),
                ('paper_height', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='طول الورق (مم)')),
                ('dpi', models.IntegerField(blank=True, null=True, verbose_name='دقة الطباعة (DPI)')),
                ('print_speed', models.CharField(choices=[('SLOW', 'بطيء'), ('NORMAL', 'عادي'), ('FAST', 'سريع')], default='NORMAL', max_length=20, verbose_name='سرعة الطباعة')),
                ('color_support', models.BooleanField(default=False, verbose_name='دعم الألوان')),
                ('duplex_support', models.BooleanField(default=False, verbose_name='دعم الطباعة على الوجهين')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='القسم')),
                ('usage_type', models.CharField(choices=[('INVOICES', 'الفواتير'), ('REPORTS', 'التقارير'), ('LABELS', 'الملصقات'), ('RECEIPTS', 'الإيصالات'), ('BARCODES', 'الباركود'), ('DOCUMENTS', 'المستندات'), ('GENERAL', 'عام')], default='GENERAL', max_length=20, verbose_name='نوع الاستخدام')),
                ('is_default', models.BooleanField(default=False, verbose_name='الطابعة الافتراضية')),
                ('is_shared', models.BooleanField(default=False, verbose_name='طابعة مشتركة')),
                ('auto_cut', models.BooleanField(default=False, verbose_name='قطع تلقائي للورق')),
                ('cash_drawer', models.BooleanField(default=False, verbose_name='فتح درج النقد')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشراء')),
                ('warranty_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء الضمان')),
                ('last_maintenance', models.DateField(blank=True, null=True, verbose_name='آخر صيانة')),
                ('next_maintenance', models.DateField(blank=True, null=True, verbose_name='الصيانة القادمة')),
                ('total_pages_printed', models.IntegerField(default=0, verbose_name='إجمالي الصفحات المطبوعة')),
                ('pages_this_month', models.IntegerField(default=0, verbose_name='صفحات هذا الشهر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('responsible_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_printers', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'طابعة',
                'verbose_name_plural': 'الطابعات',
                'ordering': ['name'],
            },
        ),
    ]
